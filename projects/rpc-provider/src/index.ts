import { agent<PERSON><PERSON>o } from './const';
import { RPC, GRPCProtocol, KessRegistry } from '@infra-node/rpc';
import CommonTransformStream from './utils/stream';
import perfLogger from './middleware/perf-logger';
import { PORT } from './const';
import { dispatchWorkFlow } from '@fastgpt/service/core/workflow/dispatch';
import {
  getDefaultEntryNodeIds,
  initWorkflowEdgeStatus,
  storeNodes2RuntimeNodes,
  textAdaptGptResponse
} from '@fastgpt/global/core/workflow/runtime/utils';
import { EventEmitter } from '@fastgpt/global/common/util/event-emitter';
import { DispatchEventMap } from '@fastgpt/global/core/workflow/type';
import { SseResponseEventEnum } from '@fastgpt/global/core/workflow/runtime/constants';

async function kwaipilotChatStream(ctx: any) {
  console.log(ctx.request.body);
  const text = ctx.request.body.prompt;
  const { nodes, edges } = JSON.parse(ctx.request.body.flow);
  const variables = {};
  ctx.body = new CommonTransformStream({ objectMode: true });

  const emitter = new EventEmitter<DispatchEventMap>();
  emitter.on('data', (t) => {
    ctx.body.write(t);
  });

  const writeResponse = (item: { data: string; event?: string }) => {
    if (item.event) {
      ctx.body.write(`event: ${item.event}\n`);
    }
    ctx.body.write(`data: ${item.data}\n\n`);
  };

  /* start flow controller */
  const { flowResponses, flowUsages, assistantResponses } = await (async () => {
    return dispatchWorkFlow({
      emitter: emitter,
      mode: 'chat',
      user: {
        _id: '123',
        username: '',
        openaiKey: '',
        status: 'active',
        createTime: 123,
        avatar: '',
        password: '',
        promotionRate: 0,
        timezone: 'Asia/Shanghai'
      },
      teamId: 'to be removed',
      tmbId: 'to be removed',
      appId: 'to be removed',
      chatId: 'to be removed',
      responseChatItemId: 'to be removed',
      runtimeNodes: storeNodes2RuntimeNodes(nodes, getDefaultEntryNodeIds(nodes)),
      runtimeEdges: initWorkflowEdgeStatus(edges),
      variables: {
        ...variables,
        userChatInput: text
      },
      inputFiles: [],
      histories: [],
      stream: true,
      detail: true,
      maxRunTimes: 200
    });
  })();

  writeResponse({
    event: SseResponseEventEnum.answer,
    data: textAdaptGptResponse({
      text: null,
      finish_reason: 'stop'
    })
  });
  writeResponse({
    event: SseResponseEventEnum.answer,
    data: '[DONE]'
  });
  writeResponse({
    event: SseResponseEventEnum.flowResponses,
    data: JSON.stringify(flowResponses)
  });

  ctx.res.end();
}

async function startupAgentProvider() {
  const registry = new KessRegistry(
    {
      serviceName: 'grpc_Example_Kwaipilot_Agent',
      owner: 'node.rpc.provider.kwaipilot'
    },
    'provider'
  );

  //agentProto
  const protocol = new GRPCProtocol({
    proto: agentProto,
    methodLowerCamelCase: false
  });

  const provider = await RPC.createProvider({
    registry,
    protocol
  });

  provider.UNSTABLE_use(perfLogger);

  provider.addService('AgentService', {
    kwaipilotChatStream
  });
  provider.start(PORT);
  console.log(`AgentProvider started at ${PORT}`);
}

startupAgentProvider();
