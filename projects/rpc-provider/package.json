{"name": "rpc-provider", "version": "0.0.1", "type": "module", "private": true, "scripts": {"build": "rollup -c"}, "dependencies": {"@fastgpt/global": "workspace:*", "@fastgpt/plugins": "workspace:*", "@fastgpt/service": "workspace:*", "@infra-node/rpc": "^2.2.1-beta.1"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.6", "@types/node": "18", "@types/react": "18.2.0", "rollup": "^4.18.0", "typescript": "4.9.5"}}