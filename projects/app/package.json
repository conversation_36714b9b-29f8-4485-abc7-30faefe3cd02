{"name": "app", "version": "4.8", "private": false, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@bany/curl-to-json": "^1.2.8", "@chakra-ui/anatomy": "2.2.1", "@chakra-ui/icons": "2.1.1", "@chakra-ui/next-js": "2.1.5", "@chakra-ui/react": "2.8.1", "@chakra-ui/styled-system": "2.9.1", "@chakra-ui/system": "2.6.1", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@fastgpt/global": "workspace:*", "@fastgpt/plugins": "workspace:*", "@fastgpt/service": "workspace:*", "@fastgpt/web": "workspace:*", "@fortaine/fetch-event-source": "^3.0.6", "@infra-node/logger": "^1.1.17", "@node-rs/jieba": "1.10.0", "@rjsf/chakra-ui": "6.0.0-alpha.0", "@rjsf/core": "6.0.0-alpha.0", "@rjsf/utils": "6.0.0-alpha.0", "@rjsf/validator-ajv8": "6.0.0-alpha.0", "@tanstack/react-query": "^4.24.10", "@types/json-schema": "^7.0.15", "@types/nprogress": "^0.2.0", "ahooks": "^3.7.11", "ajv": "^8.17.1", "axios": "^1.5.1", "date-fns": "2.30.0", "dayjs": "^1.11.7", "echarts": "5.4.1", "echarts-gl": "2.0.9", "formidable": "^2.1.1", "framer-motion": "^9.0.6", "hyperdown": "^2.4.29", "i18next": "23.10.0", "immer": "^9.0.19", "js-yaml": "^4.1.0", "json-schema-view-js": "^2.0.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mermaid": "^10.2.3", "nanoid": "^4.0.1", "next": "13.5.2", "next-i18next": "15.2.0", "nextjs-node-loader": "^1.1.5", "nprogress": "^0.2.0", "react": "18.2.0", "react-day-picker": "^8.7.1", "react-dom": "18.2.0", "react-hook-form": "7.43.1", "react-i18next": "13.5.0", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.7.4", "rehype-katex": "^6.0.2", "remark-breaks": "^3.0.3", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "request-ip": "^3.3.0", "sass": "^1.58.3", "use-context-selector": "^1.4.4", "winston": "^3.13.1", "winston-daily-rotate-file": "^5.0.0", "zustand": "^4.3.5"}, "devDependencies": {"@svgr/webpack": "^6.5.1", "@types/formidable": "^2.0.5", "@types/js-cookie": "^3.0.3", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.3", "@types/lodash": "^4.14.191", "@types/node": "^20.8.5", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-syntax-highlighter": "^15.5.6", "@types/request-ip": "^0.0.37", "eslint": "8.34.0", "eslint-config-next": "13.1.6", "nextjs-node-loader": "^1.1.5", "typescript": "4.9.5"}}