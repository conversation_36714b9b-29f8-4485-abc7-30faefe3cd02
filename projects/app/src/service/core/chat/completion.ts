import type { NextApiResponse } from 'next';
import { sseErrRes } from '@fastgpt/service/common/response';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { dispatchWorkFlow } from '@fastgpt/service/core/workflow/dispatch';
import {
  getDefaultEntryNodeIds,
  initWorkflowEdgeStatus,
  storeNodes2RuntimeNodes,
  textAdaptGptResponse
} from '@fastgpt/global/core/workflow/runtime/utils';
import { responseWrite } from '@fastgpt/service/common/response';
import { DispatchEventMap, StoreNodeItemType } from '@fastgpt/global/core/workflow/type';
import { StoreEdgeItemType } from '@fastgpt/global/core/workflow/type/edge';
import { EventEmitter } from '@fastgpt/global/common/util/event-emitter';
import {
  ArtifactChatItem,
  ChatHistoryItemResType,
  ChatItemType,
  RequestUserChatFile,
  UserChatFile,
  UserChatFileWithoutContent,
  UserChatItemValueItemType
} from '@fastgpt/global/core/chat/type';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum
} from '@fastgpt/global/core/chat/constants';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { getAvailableInputFiles } from '@fastgpt/service/common/file/read/utils';
import type { GatewayAppInfo } from '@fastgpt/global/core/workflow/type';
import { CompletionAsyncLocalStorage } from '@fastgpt/global/common/async-local-storage';

type HistoryHumanMessage = {
  role: 'user';
  content: Array<
    | {
        reply: string;
        type: 'question';
      }
    | {
        type: 'file';
        files: UserChatFileWithoutContent[];
      }
  >;
};

type HistoryAssistantMessage = {
  role: 'assistant';
  content: Array<{
    contentType: string;
    reply: string;
    sseType: 'answer' | 'flowResponses' | 'fastAnswer' | 'artifact';
    type?: 'workflow' | 'reply';
  }>;
};

export type HistoryMessage = HistoryHumanMessage | HistoryAssistantMessage;

export type WorkflowRuntimeData = {
  conversationId: string;
  appId: string;
  username: string;
  prompt: string;
  ksapAuthnToken: string;
  nodes: StoreNodeItemType[];
  edges: StoreEdgeItemType[];
  gatewayAppInfo: GatewayAppInfo;
  histories?: Array<HistoryMessage>;
  variables?: Record<string, any>;
  files?: RequestUserChatFile[];
};

export type AnswerContent = {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    delta: {
      role?: string;
      content?: string;
    };
    index: number;
  }[];
};

const logger = createLogger('service-chat-completion');

export async function handleChatCompletion(res: NextApiResponse, data: WorkflowRuntimeData) {
  const eventEmitter = new EventEmitter<DispatchEventMap>();
  // TODO: 不支持 variables？
  const {
    appId,
    conversationId,
    prompt,
    histories = [],
    username,
    variables = {},
    nodes,
    edges,
    files = [],
    ksapAuthnToken,
    gatewayAppInfo
  } = data;
  const store = CompletionAsyncLocalStorage.getStore();
  const traceId = store?.traceId ?? '';
  res.on('close', () => {
    logger.info('handle completion res close', undefined, {
      conversationId,
      appId,
      username,
      traceId
    });
    eventEmitter.close();
    res.end();
  });
  res.on('error', (err) => {
    logger.error(`handle completion res error ${err.message}`, err, {
      conversationId,
      appId,
      username,
      traceId
    });
    eventEmitter.close();
    res.end();
  });

  logger.info(`handle completion gatewayAppInfo ${gatewayAppInfo.id}`, gatewayAppInfo);

  const userFiles: UserChatFileWithoutContent[] = files.map((f) => ({ ...f }));
  res.setHeader('Content-Type', 'text/event-stream;charset=utf-8');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('X-Accel-Buffering', 'no');
  res.setHeader('Cache-Control', 'no-cache, no-transform');

  eventEmitter.on('data', (t) => {
    res.write(t);
  });

  // 只保留最多 20 条历史记录
  histories.splice(0, Math.max(0, histories.length - 20));

  const chatHistories: ChatItemType[] = [];
  for (let i = histories.length - 1; i >= 0; i--) {
    const h = histories[i];
    if (h.role === 'user') {
      const values: UserChatItemValueItemType[] = [];
      h.content.forEach((c) => {
        if (c.type === 'file') {
          // 文件
          c.files?.forEach((f) => {
            if (f) {
              values.push({
                type: ChatItemValueTypeEnum.file,
                file: {
                  ...f,
                  pages: [],
                  content: '',
                  index: -1
                }
              });

              // 待解析的文件
              userFiles.push({
                type: f.type === 'image' ? ChatFileTypeEnum.image : ChatFileTypeEnum.file,
                name: f.name,
                url: f.url
              });
            }
          });
        } else {
          // text
          values.push({
            type: ChatItemValueTypeEnum.text,
            text: {
              content: c.reply
            }
          });
        }
      });

      chatHistories.unshift({
        obj: ChatRoleEnum.Human,
        value: values
      });
    } else if (h.role === 'assistant') {
      const answers = h.content.filter(
        (c) => c.type === 'workflow' && (c.sseType === 'answer' || c.sseType === 'fastAnswer')
      );
      const flowResponses = h.content.filter(
        (c) => c.type === 'workflow' && c.sseType === 'flowResponses'
      );

      const artifacts = h.content
        .filter((c) => c.type === 'workflow' && c.sseType === 'artifact')
        .map((c) => {
          try {
            return JSON.parse(c.reply) as ArtifactChatItem;
          } catch (err) {
            logger.error(`parse artifact error ${err}`, err);
            return null;
          }
        })
        .filter((a) => a) as ArtifactChatItem[];

      const texts: string[] = h.content.filter((c) => c.type === 'reply').map((c) => c.reply);
      answers.forEach((a) => {
        try {
          const obj = JSON.parse(a.reply) as AnswerContent;
          const text = obj.choices.reduce((acc, c) => acc + (c.delta.content ?? ''), '');
          texts.push(text);
        } catch (err) {
          console.error(err);
        }
      });

      let responseData: ChatHistoryItemResType[] = [];
      flowResponses.forEach((f) => {
        try {
          responseData = JSON.parse(f.reply) as ChatHistoryItemResType[];
        } catch (err) {
          console.error(err);
        }
      });

      chatHistories.unshift({
        obj: ChatRoleEnum.AI,
        value: texts.map((t) => ({
          type: ChatItemValueTypeEnum.text,
          text: {
            content: t
          }
        })),
        [DispatchNodeResponseKeyEnum.nodeResponse]: responseData,
        [DispatchNodeResponseKeyEnum.artifacts]: artifacts.filter((a) => a)
      });
    }
  }

  // 获取文件内容
  const formerFiles = await getAvailableInputFiles(userFiles);
  chatHistories.forEach((c) => {
    if (c.obj !== ChatRoleEnum.Human) {
      return;
    }

    c.value.forEach((v) => {
      if (v.type === ChatItemValueTypeEnum.file && v.file) {
        const valueFile = v.file;
        const item = formerFiles.find((f) => f.url === valueFile.url);
        valueFile.content = item?.content ?? '';
        valueFile.pages = item?.pages ?? [];
      }
    });
  });

  const inputFiles: UserChatFile[] = files.map((f) => {
    const item = formerFiles.find((v) => v.url === f.url);
    return {
      ...f,
      content: item?.content ?? '',
      pages: item?.pages ?? [],
      index: item?.index ?? -1
    };
  });

  /* start flow controller */
  const { flowResponses, flowUsages, assistantResponses } = await (async () => {
    return dispatchWorkFlow({
      emitter: eventEmitter,
      mode: 'chat',
      user: {
        username
      },
      conversationId,
      traceId,
      appId: appId,
      runtimeNodes: storeNodes2RuntimeNodes(nodes, getDefaultEntryNodeIds(nodes)),
      runtimeEdges: initWorkflowEdgeStatus(edges),
      variables: {
        ...variables,
        appId,
        userChatInput: prompt,
        username
      },
      inputFiles: inputFiles,
      histories: chatHistories,
      stream: true,
      detail: true,
      maxRunTimes: 200,
      ksapAuthnToken,
      gatewayAppInfo
    });
  })();

  const feResponseData = flowResponses;

  logger.info('send stop answer');
  responseWrite({
    res,
    event: SseResponseEventEnum.answer,
    data: textAdaptGptResponse({
      text: null,
      finish_reason: 'stop'
    })
  });

  logger.info('send DONE answer');
  responseWrite({
    res,
    event: SseResponseEventEnum.answer,
    data: '[DONE]'
  });

  logger.info('send flowResponses answer');
  responseWrite({
    res,
    event: SseResponseEventEnum.flowResponses,
    data: JSON.stringify(feResponseData)
  });
}
