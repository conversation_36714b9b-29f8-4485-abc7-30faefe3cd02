import { jsonRes } from '@fastgpt/service/common/response';
import type { NextApiResponse, NextApiHandler, NextApiRequest } from 'next';
import { withNextCors } from '@fastgpt/service/common/middle/cors';
import { createLogger, perLogger } from '@fastgpt/global/common/util/logger';
import { parse } from 'url';
import {
  CompletionAsyncLocalStorage,
  CompletionAsyncLocalStorageType
} from '@fastgpt/global/common/async-local-storage';
import { getNanoid } from '@fastgpt/global/common/string/tools';
import { getAppInfoFromRequest } from '../../utils/reqInfo';
import requestIp from 'request-ip';

const logger = createLogger('core-middle-entry');

export const NextAPI = (...args: NextApiHandler[]): NextApiHandler => {
  return async function api(req: NextApiRequest, res: NextApiResponse) {
    const originIp = requestIp.getClientIp(req) ?? '';
    // 从请求头里获取 x-kwaipilot-trace-id
    const traceIdHeader = req.headers['x-kwaipilot-trace-id'];
    const traceId = Array.isArray(traceIdHeader) ? traceIdHeader[0] : traceIdHeader ?? getNanoid();
    const { appId, conversationId, username } = getAppInfoFromRequest(req);
    const store: CompletionAsyncLocalStorageType = {
      traceId,
      originIp,
      appId,
      conversationId,
      username
    };
    CompletionAsyncLocalStorage.run(store, async () => {
      try {
        const start = +new Date();
        const { pathname } = parse(req.url || '', true);
        const status = String(res.statusCode);

        logger.info(`access hit ${pathname} ${appId} ${status} ${traceId} ${originIp}`, req.body);

        await Promise.all([withNextCors(req, res)]);

        let response = null;
        for (const handler of args) {
          response = await handler(req, res);
        }

        const cost = +new Date() - start;
        perLogger.perf({
          subtag: 'access',
          millis: cost,
          extra1: pathname || '',
          extra2: String(res.statusCode)
        });
        logger.info(
          `access success ${pathname} ${appId} ${status} ${traceId} ${originIp} ${cost}ms`
        );
        if (!res.writableFinished) {
          return jsonRes(res, {
            code: 200,
            data: response
          });
        }
      } catch (error) {
        logger.error(`access error for ${req.url} ${appId} ${traceId} ${originIp}`, {
          error: error
        });
        return jsonRes(res, {
          code: 500,
          error,
          url: req.url
        });
      }
    });
  };
};
