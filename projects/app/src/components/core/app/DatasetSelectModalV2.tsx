import React, { useEffect, useMemo, useState } from 'react';
import {
  Card,
  Flex,
  Box,
  Button,
  ModalBody,
  ModalFooter,
  useTheme,
  Grid,
  Divider
} from '@chakra-ui/react';
import Avatar from '@/components/Avatar';
import type { SelectedDatasetTypeV2 } from '@fastgpt/global/core/workflow/api.d';
import { useToast } from '@fastgpt/web/hooks/useToast';
import MyTooltip from '@/components/MyTooltip';
import MyIcon from '@fastgpt/web/components/common/Icon';
import { DatasetTypeEnum } from '@fastgpt/global/core/dataset/constants';
import { useTranslation } from 'next-i18next';
import { useDatasetStore } from '@/web/core/datasetV2/store/dataset';
import DatasetSelectContainer from '@/components/core/dataset/SelectModalV2';
import { useLoading } from '@fastgpt/web/hooks/useLoading';
import EmptyTip from '@fastgpt/web/components/common/EmptyTip';
import { DatasetItem } from '@/web/core/datasetV2/api';

export const DatasetSelectModal = ({
  isOpen,
  defaultSelectedDatasets = [],
  onChange,
  onClose
}: {
  isOpen: boolean;
  defaultSelectedDatasets: SelectedDatasetTypeV2;
  onChange: (e: SelectedDatasetTypeV2) => void;
  onClose: () => void;
}): JSX.Element => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { allDatasets } = useDatasetStore();
  const [selectedDatasets, setSelectedDatasets] = useState<SelectedDatasetTypeV2>(
    defaultSelectedDatasets.filter((dataset) => {
      return allDatasets.find((item) => item.id === dataset.datasetId);
    })
  );

  // const { Loading, setIsLoading } = useLoading();
  const filterDatasets = useMemo(() => {
    return {
      selected: allDatasets.filter((item) =>
        selectedDatasets.find((dataset) => dataset.datasetId === item.id)
      ),
      unSelected: allDatasets.filter(
        (item) => !selectedDatasets.find((dataset) => dataset.datasetId === item.id)
      )
    };
  }, [allDatasets, selectedDatasets]);

  return (
    <DatasetSelectContainer
      isOpen={isOpen}
      tips={t('dataset.Select Dataset Tips')}
      onClose={onClose}
    >
      <Flex h={'100%'} flexDirection={'column'} flex={'1 0 0'}>
        <ModalBody flex={'1 0 0'} overflowY={'auto'} userSelect={'none'}>
          <Grid
            gridTemplateColumns={[
              'repeat(1, minmax(0, 1fr))',
              'repeat(2, minmax(0, 1fr))',
              'repeat(3, minmax(0, 1fr))'
            ]}
            gridGap={3}
          >
            {filterDatasets.selected.map((item) =>
              (() => {
                return (
                  <Card
                    key={item.id}
                    p={3}
                    border={theme.borders.base}
                    boxShadow={'sm'}
                    bg={'primary.200'}
                  >
                    <Flex alignItems={'center'} h={'38px'}>
                      <Avatar src={item.icon} w={['24px', '28px']}></Avatar>
                      <Box flex={'1 0 0'} w={0} fontSize={'16px'} className="textEllipsis" mx={3}>
                        {item.name}
                      </Box>
                      <MyIcon
                        name={'delete'}
                        w={'14px'}
                        cursor={'pointer'}
                        _hover={{ color: 'red.500' }}
                        onClick={() => {
                          setSelectedDatasets((state) =>
                            state.filter((kb) => kb.datasetId !== item.id)
                          );
                        }}
                      />
                    </Flex>
                  </Card>
                );
              })()
            )}
          </Grid>

          {filterDatasets.selected.length > 0 && <Divider my={3} />}

          <Grid
            gridTemplateColumns={[
              'repeat(1, minmax(0, 1fr))',
              'repeat(2, minmax(0, 1fr))',
              'repeat(3, minmax(0, 1fr))'
            ]}
            gridGap={3}
          >
            {filterDatasets.unSelected.map((item) =>
              (() => {
                return (
                  <MyTooltip key={item.id} label={t('dataset.Select Folder')}>
                    <Card
                      p={3}
                      border={theme.borders.base}
                      boxShadow={'sm'}
                      h={'80px'}
                      cursor={'pointer'}
                      _hover={{
                        boxShadow: 'md'
                      }}
                      onClick={() => {
                        setSelectedDatasets((state) => [...state, { datasetId: item.id }]);
                      }}
                    >
                      <Flex alignItems={'center'} h={'38px'}>
                        <Avatar src={item.icon} w={['24px', '28px']}></Avatar>
                        <Box flex={'1 0 0'} w={0} className="textEllipsis" ml={3} fontSize={'16px'}>
                          {item.name}
                        </Box>
                      </Flex>
                      <Flex justifyContent={'flex-end'} alignItems={'center'} fontSize={'sm'}>
                        <Box color={'myGray.500'}>被引用{item.refCount}次</Box>
                      </Flex>
                    </Card>
                  </MyTooltip>
                );
              })()
            )}
          </Grid>
          {filterDatasets.unSelected.length === 0 && <EmptyTip text={t('common.folder.empty')} />}
        </ModalBody>

        <ModalFooter>
          <Button
            onClick={() => {
              // filter out the dataset that is not in the kList
              const filterDatasets = selectedDatasets.filter((dataset) => {
                return allDatasets.find((item) => item.id === dataset.datasetId);
              });

              onClose();
              onChange(filterDatasets);
            }}
          >
            {t('common.Done')}
          </Button>
        </ModalFooter>

        {/* <Loading fixed={false} loading={isFetching} /> */}
      </Flex>
    </DatasetSelectContainer>
  );
};

export default DatasetSelectModal;
