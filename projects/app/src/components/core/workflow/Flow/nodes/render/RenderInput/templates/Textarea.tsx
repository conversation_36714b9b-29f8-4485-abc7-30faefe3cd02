import React, { useCallback, useMemo } from 'react';
import type { RenderInputProps } from '../type';
import { useTranslation } from 'next-i18next';
import PromptEditor from '@fastgpt/web/components/common/Textarea/PromptEditor';
import { formatEditorVariablePickerIcon } from '@fastgpt/global/core/workflow/utils';
import { useContextSelector } from 'use-context-selector';
import { WorkflowContext } from '@/components/core/workflow/context';
import {
  getWorkflowGlobalVariables,
  getWorkflowPreviousVariables
} from '@/web/core/workflow/utils';
import { useCreation } from 'ahooks';
import { Box } from '@chakra-ui/react';

const TextareaRender = ({ inputs = [], item, nodeId }: RenderInputProps) => {
  const { t } = useTranslation();
  const edges = useContextSelector(WorkflowContext, (v) => v.edges);
  const nodeList = useContextSelector(WorkflowContext, (v) => v.nodeList);
  const onChangeNode = useContextSelector(WorkflowContext, (v) => v.onChangeNode);

  // get variable
  const variables = useCreation(() => {
    const globalVariables = getWorkflowGlobalVariables(nodeList, t);

    const moduleVariables = formatEditorVariablePickerIcon(
      inputs
        .filter((input) => input.canEdit)
        .map((item) => ({
          key: item.key,
          label: item.label
        }))
    );

    const previousVariables = getWorkflowPreviousVariables(nodeList, edges, nodeId, t);

    return [...globalVariables, ...moduleVariables, ...previousVariables];
  }, [nodeList, inputs, t, edges, nodeId]);

  const onChange = useCallback(
    (e: string) => {
      onChangeNode({
        nodeId,
        type: 'updateInput',
        key: item.key,
        value: {
          ...item,
          value: e
        }
      });
    },
    [item, nodeId, onChangeNode]
  );
  const Render = useMemo(() => {
    return (
      // 不再需要设置 nowheel className，组件中自己处理，否则双指缩放会触发到浏览器的缩放
      // https://team.corp.kuaishou.com/task/B2076380
      <Box>
        <PromptEditor
          variables={variables}
          title={t(item.label)}
          maxLength={item.maxLength}
          h={150}
          placeholder={t(item.placeholder || '')}
          value={item.value}
          onChange={onChange}
          isFlow={true}
        />
      </Box>
    );
  }, [item.label, item.maxLength, item.placeholder, item.value, onChange, t, variables]);

  return Render;
};

export default React.memo(TextareaRender);
