import { PostWorkflowDebugProps, PostWorkflowDebugResponse } from '@/global/core/workflow/api';
import {
  Agent,
  AgentUpdateParams,
  LLMModelInfo,
  FormatLLMModelInfo
} from '@fastgpt/global/core/app/type';
import { getModelConfig } from '@fastgpt/global/core/chat/utils';
import { ModuleIcons } from '@fastgpt/global/core/workflow/constants';
import { FlowNodeTypeEnum } from '@fastgpt/global/core/workflow/node/constant';
import axios, { AxiosResponse } from 'axios';

export const instance = axios.create({
  baseURL: process.env.NODE_ENV === 'development' ? '/api/mock' : '/api'
});

instance.interceptors.response.use((response) => {
  if (!response.data) {
    throw Error('response data is empty');
  }
  // 后端出错时，可能会在 data 中放置 node 信息
  if (response.data.status !== 200 && response.data.code !== 200) {
    const err = new Error(response.data.message);
    if (response.data?.data) {
      (err as any).data = response.data.data;
    }
    throw err;
  }
  return response;
});

export const getAgentDetail = async (id: number, agentId: string) => {
  const res = await instance.get<{ data: Agent }>(`/kap/agent/getDetail?auid=${agentId}&id=${id}`);
  if (res.data.data.agentMode === 'WORKFLOW') {
    res.data.data.workFlow.nodes.forEach((n) => {
      if (!n.avatar?.startsWith('/imgs/workflow/')) {
        return;
      }

      // 兜底替换图标，兼容存量数据
      if (n.flowNodeType === FlowNodeTypeEnum.answerNode) {
        n.avatar = ModuleIcons.Reply;
      } else if (n.flowNodeType === FlowNodeTypeEnum.systemConfig) {
        n.avatar = ModuleIcons.SystemConfig;
      } else if (n.flowNodeType === FlowNodeTypeEnum.classifyQuestion) {
        n.avatar = ModuleIcons.ClassifyQuestion;
      } else if (n.flowNodeType === FlowNodeTypeEnum.chatNode) {
        n.avatar = ModuleIcons.AIChat;
      } else if (n.flowNodeType === FlowNodeTypeEnum.httpRequest468) {
        n.avatar = ModuleIcons.HTTP;
      } else if (n.flowNodeType === FlowNodeTypeEnum.httpTool) {
        n.avatar = ModuleIcons.Tool;
      } else if (
        n.flowNodeType === FlowNodeTypeEnum.datasetSearchNode ||
        n.flowNodeType === FlowNodeTypeEnum.datasetSearchNodeV2
      ) {
        n.avatar = ModuleIcons.Dataset;
      } else if (n.flowNodeType === FlowNodeTypeEnum.ifElseNode) {
        n.avatar = ModuleIcons.IfElse;
      } else if (n.flowNodeType === FlowNodeTypeEnum.contentExtract) {
        n.avatar = ModuleIcons.Extract;
      } else if (n.flowNodeType === FlowNodeTypeEnum.workflowStart) {
        n.avatar = ModuleIcons.Start;
      } else if (n.flowNodeType === FlowNodeTypeEnum.globalVariable) {
        n.avatar = ModuleIcons.GlobalVariable;
      } else if (n.flowNodeType === FlowNodeTypeEnum.kconf) {
        n.avatar = ModuleIcons.Kconf;
      } else if (n.flowNodeType === FlowNodeTypeEnum.codeInterpreter) {
        n.avatar = ModuleIcons.CodeInterpreter;
      } else if (n.flowNodeType === FlowNodeTypeEnum.templateTransformNode) {
        n.avatar = ModuleIcons.Template;
      } else if (n.flowNodeType === FlowNodeTypeEnum.groupVariable) {
        n.avatar = ModuleIcons.Aggregation;
      }
    });
  }
  return res.data.data;
};

export const updateAgentDetail = async (data: AgentUpdateParams) => {
  const body = {
    ...data,
    maintainers: data.maintainers.map((item) => item.username)
  };
  const res = await instance.post<{ data: { id: number; auid: string } }>(
    '/kap/agent/update',
    body
  );
  return res.data.data;
};

export const getLLMModelList = async (auid: string): Promise<FormatLLMModelInfo[]> => {
  const res = await instance.get<{ data: LLMModelInfo[] }>(
    `/kap/common/get_chat_model_list?auid=${auid}`
  );
  return res.data.data.map((m) => {
    const config = getModelConfig(m.modelType);
    return {
      ...m,
      ...config
    };
  });
};

export const postDebug = async (data: PostWorkflowDebugProps) => {
  const res = await instance.post<
    PostWorkflowDebugProps,
    AxiosResponse<{
      code: number;
      message: string;
      data: PostWorkflowDebugResponse;
    }>
  >('/kap/conversation/workflow/debug', data, {
    headers: {
      // 'x-debug-workflow-ip': '************',
      // 'x-debug-workflow-port': '3000'
    }
  });
  if (res.data && res.data.code === 200) {
    return res.data.data;
  }
  throw Error(res.data?.message || 'internal error');
};
