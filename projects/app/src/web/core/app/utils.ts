import { StoreNodeItemType } from '@fastgpt/global/core/workflow/type/index.d';
import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '@fastgpt/global/core/workflow/node/constant';
import {
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  WorkflowIOValueTypeEnum
} from '@fastgpt/global/core/workflow/constants';

import { getNanoid } from '@fastgpt/global/common/string/tools';
import { StoreEdgeItemType } from '@fastgpt/global/core/workflow/type/edge';
import { FlowNodeInputItemType } from '@fastgpt/global/core/workflow/type/io';
import { Input_Template_UserChatInput } from '@fastgpt/global/core/workflow/template/input';
import {
  getUserFilesInputItem,
  getUserFilesOutputItem
} from '@fastgpt/global/core/workflow/template/system/workflowStart';

import { createLogger } from '@fastgpt/global/common/util/logger';
import {
  getTypesFromSchema,
  KwaipilotToolInputRawType,
  KwaipilotToolInputRawTypeLocation,
  KwaipilotToolSchemaParseError,
  getToolRawTypeFromShallowSchema
} from '@fastgpt/global/core/plugin/kwaipilotTool/utils';

type WorkflowType = {
  nodes: StoreNodeItemType[];
  edges: StoreEdgeItemType[];
};

const logger = createLogger('web-core-app-utils');

export type KwaipilotTool = {
  name: string;
  description?: string;
  apiMethod: string;
  enterParamSchema?: {
    bodyParamSchema?: string;
    headerParamSchema?: string;
    pathParamSchema?: string;
    queryParamSchema?: string;
  };
  outParamSchema?: string;
  code: string; // 唯一标识
  icon: string;
  tuid: string; // 工具id
  id: number; // 版本 id
};

export type CodeSearch = {
  repoName: string;
  commitId: string;
  targetDirectory: string[];
};

export type NormalWorkflowData = {
  model: string;
  temperature: number;
  maxToken: number;
  roleDesc: string;
  maxHistories: number;
  knowledgeRepoIds: number[];
  tools: KwaipilotTool[];
  codeSearch?: CodeSearch;
};

export type ToolDebugWorkflow = {
  tool: KwaipilotTool;
  reqParam: {
    header?: string;
    query?: string;
    path?: string;
    body?: string;
  };
};

export function form2NormalWorkflow(data: NormalWorkflowData): WorkflowType {
  const workflowStartNodeId = 'workflowStartNodeId';
  function systemConfigTemplate(): StoreNodeItemType {
    return {
      nodeId: 'userGuide',
      name: '系统配置',
      intro: '可以配置应用的系统参数',
      flowNodeType: FlowNodeTypeEnum.systemConfig,
      position: {
        x: 531.2422736065552,
        y: -486.7611729549753
      },
      inputs: [
        {
          key: NodeInputKeyEnum.welcomeText,
          renderTypeList: [FlowNodeInputTypeEnum.hidden],
          label: 'core.app.Welcome Text',
          value: ''
        },
        {
          key: NodeInputKeyEnum.recommendedQuestion,
          renderTypeList: [FlowNodeInputTypeEnum.hidden],
          label: '',
          value: []
        }
      ],
      outputs: []
    };
  }
  function workflowStartTemplate(): StoreNodeItemType {
    return {
      nodeId: workflowStartNodeId,
      name: '流程开始',
      intro: '',
      avatar: '/imgs/workflow/userChatInput.svg',
      flowNodeType: FlowNodeTypeEnum.workflowStart,
      position: {
        x: 558.4082376415505,
        y: 123.72387429194112
      },
      inputs: [
        {
          ...Input_Template_UserChatInput,
          toolDescription: '用户问题'
        },
        getUserFilesInputItem()
      ],
      outputs: [
        {
          id: NodeOutputKeyEnum.userChatInput,
          key: NodeOutputKeyEnum.userChatInput,
          label: 'core.module.input.label.user question',
          type: FlowNodeOutputTypeEnum.static,
          valueType: WorkflowIOValueTypeEnum.string
        },
        getUserFilesOutputItem()
      ]
    };
  }

  function simpleChatTemplate(formData: NormalWorkflowData): WorkflowType {
    return {
      nodes: [
        {
          nodeId: '7BdojPlukIQw',
          name: 'AI 对话',
          intro: 'AI 大模型对话',
          avatar: '/imgs/workflow/AI.png',
          flowNodeType: FlowNodeTypeEnum.chatNode,
          showStatus: true,
          position: {
            x: 1106.3238387960757,
            y: -350.6030674683474
          },
          inputs: [
            {
              key: NodeInputKeyEnum.aiModel,
              renderTypeList: [
                FlowNodeInputTypeEnum.settingLLMModel,
                FlowNodeInputTypeEnum.reference
              ],
              label: 'core.module.input.label.aiModel',
              valueType: WorkflowIOValueTypeEnum.string,
              value: formData.model
            },
            {
              key: NodeInputKeyEnum.aiChatTemperature,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: formData.temperature,
              valueType: WorkflowIOValueTypeEnum.number,
              min: 0,
              max: 10,
              step: 1
            },
            {
              key: NodeInputKeyEnum.aiChatMaxToken,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: formData.maxToken,
              valueType: WorkflowIOValueTypeEnum.number,
              min: 100,
              max: 4000,
              step: 50
            },
            {
              key: NodeInputKeyEnum.aiChatIsResponseText,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: true,
              valueType: WorkflowIOValueTypeEnum.boolean
            },
            {
              key: NodeInputKeyEnum.aiChatUseInputFiles,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: true,
              valueType: WorkflowIOValueTypeEnum.boolean
            },
            {
              key: NodeInputKeyEnum.aiChatAdvancedQuote,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: true,
              valueType: WorkflowIOValueTypeEnum.boolean
            },
            {
              key: NodeInputKeyEnum.aiChatQuoteTemplate,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              valueType: WorkflowIOValueTypeEnum.string
            },
            {
              key: NodeInputKeyEnum.aiChatQuotePrompt,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              valueType: WorkflowIOValueTypeEnum.string
            },
            {
              key: NodeInputKeyEnum.aiSystemPrompt,
              renderTypeList: [FlowNodeInputTypeEnum.textarea, FlowNodeInputTypeEnum.reference],
              max: 3000,
              valueType: WorkflowIOValueTypeEnum.string,
              label: 'core.ai.Prompt',
              description: 'core.app.tip.chatNodeSystemPromptTip',
              placeholder: 'core.app.tip.chatNodeSystemPromptTip',
              value: formData.roleDesc
            },
            {
              key: NodeInputKeyEnum.history,
              renderTypeList: [FlowNodeInputTypeEnum.numberInput, FlowNodeInputTypeEnum.reference],
              valueType: WorkflowIOValueTypeEnum.chatHistory,
              label: 'core.module.input.label.chat history',
              required: true,
              min: 0,
              max: 30,
              value: formData.maxHistories
            },
            {
              key: NodeInputKeyEnum.userChatInput,
              renderTypeList: [FlowNodeInputTypeEnum.reference, FlowNodeInputTypeEnum.textarea],
              valueType: WorkflowIOValueTypeEnum.string,
              label: '用户问题',
              required: true,
              toolDescription: '用户问题',
              value: [workflowStartNodeId, 'userChatInput']
            },
            {
              key: NodeInputKeyEnum.aiChatDatasetQuote,
              renderTypeList: [FlowNodeInputTypeEnum.settingDatasetQuotePrompt],
              label: '',
              debugLabel: '知识库引用',
              description: '',
              valueType: WorkflowIOValueTypeEnum.datasetQuote
            }
          ],
          outputs: [
            {
              id: 'history',
              key: NodeOutputKeyEnum.history,
              label: 'core.module.output.label.New context',
              description: 'core.module.output.description.New context',
              valueType: WorkflowIOValueTypeEnum.chatHistory,
              type: 'static'
            },
            {
              id: 'answerText',
              key: NodeOutputKeyEnum.answerText,
              label: 'core.module.output.label.Ai response content',
              description: 'core.module.output.description.Ai response content',
              valueType: WorkflowIOValueTypeEnum.string,
              type: 'static'
            }
          ]
        }
      ],
      edges: [
        {
          source: workflowStartNodeId,
          target: '7BdojPlukIQw',
          sourceHandle: `${workflowStartNodeId}-source-right`,
          targetHandle: '7BdojPlukIQw-target-left'
        }
      ]
    };
  }
  function onlyDatasetTemplate(formData: NormalWorkflowData): WorkflowType {
    return {
      nodes: [
        {
          nodeId: '7BdojPlukIQw',
          name: 'AI 对话',
          intro: 'AI 大模型对话',
          avatar: '/imgs/workflow/AI.png',
          flowNodeType: FlowNodeTypeEnum.chatNode,
          showStatus: true,
          position: {
            x: 1638.509551404687,
            y: -341.0428450861567
          },
          inputs: [
            {
              key: NodeInputKeyEnum.aiModel,
              renderTypeList: [
                FlowNodeInputTypeEnum.settingLLMModel,
                FlowNodeInputTypeEnum.reference
              ],
              label: 'core.module.input.label.aiModel',
              valueType: WorkflowIOValueTypeEnum.string,
              value: formData.model
            },
            {
              key: NodeInputKeyEnum.aiChatTemperature,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: formData.temperature,
              valueType: WorkflowIOValueTypeEnum.number,
              min: 0,
              max: 10,
              step: 1
            },
            {
              key: NodeInputKeyEnum.aiChatMaxToken,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: formData.maxToken,
              valueType: WorkflowIOValueTypeEnum.number,
              min: 100,
              max: 4000,
              step: 50
            },
            {
              key: NodeInputKeyEnum.aiChatIsResponseText,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: true,
              valueType: WorkflowIOValueTypeEnum.boolean
            },
            {
              key: NodeInputKeyEnum.aiChatUseInputFiles,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: true,
              valueType: WorkflowIOValueTypeEnum.boolean
            },
            {
              key: NodeInputKeyEnum.aiChatQuoteTemplate,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              valueType: WorkflowIOValueTypeEnum.string
            },
            {
              key: NodeInputKeyEnum.aiChatQuotePrompt,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              valueType: WorkflowIOValueTypeEnum.string
            },
            {
              key: NodeInputKeyEnum.aiSystemPrompt,
              renderTypeList: [FlowNodeInputTypeEnum.textarea, FlowNodeInputTypeEnum.reference],
              max: 3000,
              valueType: WorkflowIOValueTypeEnum.string,
              label: 'core.ai.Prompt',
              description: 'core.app.tip.chatNodeSystemPromptTip',
              placeholder: 'core.app.tip.chatNodeSystemPromptTip',
              value: formData.roleDesc
            },
            {
              key: NodeInputKeyEnum.history,
              renderTypeList: [FlowNodeInputTypeEnum.numberInput, FlowNodeInputTypeEnum.reference],
              valueType: WorkflowIOValueTypeEnum.chatHistory,
              label: 'core.module.input.label.chat history',
              required: true,
              min: 0,
              max: 30,
              value: formData.maxHistories
            },
            {
              key: NodeInputKeyEnum.userChatInput,
              renderTypeList: [FlowNodeInputTypeEnum.reference, FlowNodeInputTypeEnum.textarea],
              valueType: WorkflowIOValueTypeEnum.string,
              label: '用户问题',
              required: true,
              toolDescription: '用户问题',
              value: [workflowStartNodeId, 'userChatInput']
            },
            {
              key: NodeInputKeyEnum.aiChatDatasetQuote,
              renderTypeList: [FlowNodeInputTypeEnum.settingDatasetQuotePrompt],
              label: '',
              debugLabel: '知识库引用',
              description: '',
              valueType: WorkflowIOValueTypeEnum.datasetQuote,
              value: ['iKBoX2vIzETU', 'quoteQAV2']
            }
          ],
          outputs: [
            {
              id: 'history',
              key: NodeOutputKeyEnum.history,
              label: 'core.module.output.label.New context',
              description: 'core.module.output.description.New context',
              valueType: WorkflowIOValueTypeEnum.chatHistory,
              type: 'static'
            },
            {
              id: 'answerText',
              key: NodeOutputKeyEnum.answerText,
              label: 'core.module.output.label.Ai response content',
              description: 'core.module.output.description.Ai response content',
              valueType: WorkflowIOValueTypeEnum.string,
              type: 'static'
            }
          ]
        },
        {
          nodeId: 'iKBoX2vIzETU',
          name: '知识库搜索',
          intro: '调用“语义检索”和“全文检索”能力，从“知识库”中查找可能与问题相关的参考内容',
          avatar: '/imgs/workflow/db.png',
          flowNodeType: FlowNodeTypeEnum.datasetSearchNodeV2,
          showStatus: true,
          position: {
            x: 918.5901682164496,
            y: -227.11542247619582
          },
          inputs: [
            {
              key: NodeInputKeyEnum.datasetSelectListV2,
              renderTypeList: [FlowNodeInputTypeEnum.selectDatasetV2],
              label: 'core.module.input.label.Select dataset',
              value: (formData.knowledgeRepoIds ?? []).map((k) => ({
                datasetId: k
              })),
              valueType: WorkflowIOValueTypeEnum.selectDatasetV2,
              list: [],
              required: true
            },
            {
              key: NodeInputKeyEnum.userChatInput,
              renderTypeList: [FlowNodeInputTypeEnum.reference, FlowNodeInputTypeEnum.textarea],
              valueType: WorkflowIOValueTypeEnum.string,
              label: '用户问题',
              required: true,
              toolDescription: '需要检索的内容',
              value: [workflowStartNodeId, 'userChatInput']
            }
          ],
          outputs: [
            {
              id: 'quoteQAV2',
              key: NodeOutputKeyEnum.datasetQuoteQAV2,
              label: 'core.module.Dataset quote.label',
              type: 'static',
              valueType: WorkflowIOValueTypeEnum.arrayString
            }
          ]
        }
      ],
      edges: [
        {
          source: workflowStartNodeId,
          target: 'iKBoX2vIzETU',
          sourceHandle: `${workflowStartNodeId}-source-right`,
          targetHandle: 'iKBoX2vIzETU-target-left'
        },
        {
          source: 'iKBoX2vIzETU',
          target: '7BdojPlukIQw',
          sourceHandle: 'iKBoX2vIzETU-source-right',
          targetHandle: '7BdojPlukIQw-target-left'
        }
      ]
    };
  }

  function onlyCodeSearchTemplate(formData: NormalWorkflowData): WorkflowType {
    return {
      nodes: [
        {
          nodeId: '7BdojPlukIQw',
          name: 'AI 对话',
          intro: 'AI 大模型对话',
          avatar: '/imgs/workflow/AI.png',
          flowNodeType: 'chatNode',
          showStatus: true,
          position: {
            x: 1638.509551404687,
            y: -341.0428450861567
          },
          inputs: [
            {
              key: NodeInputKeyEnum.aiModel,
              renderTypeList: [
                FlowNodeInputTypeEnum.settingLLMModel,
                FlowNodeInputTypeEnum.reference
              ],
              label: 'core.module.input.label.aiModel',
              valueType: WorkflowIOValueTypeEnum.string,
              value: formData.model
            },
            {
              key: NodeInputKeyEnum.aiChatTemperature,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: formData.temperature,
              valueType: WorkflowIOValueTypeEnum.number,
              min: 0,
              max: 10,
              step: 1
            },
            {
              key: NodeInputKeyEnum.aiChatMaxToken,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: formData.maxToken,
              valueType: WorkflowIOValueTypeEnum.number,
              min: 100,
              max: 4000,
              step: 50
            },
            {
              key: NodeInputKeyEnum.aiChatIsResponseText,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: true,
              valueType: WorkflowIOValueTypeEnum.boolean
            },
            {
              key: NodeInputKeyEnum.aiChatUseInputFiles,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              value: true,
              valueType: WorkflowIOValueTypeEnum.boolean
            },
            {
              key: NodeInputKeyEnum.aiChatQuoteTemplate,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              valueType: WorkflowIOValueTypeEnum.string
            },
            {
              key: NodeInputKeyEnum.aiChatQuotePrompt,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              label: '',
              valueType: WorkflowIOValueTypeEnum.string
            },
            {
              key: NodeInputKeyEnum.aiSystemPrompt,
              renderTypeList: [FlowNodeInputTypeEnum.textarea, FlowNodeInputTypeEnum.reference],
              max: 3000,
              valueType: WorkflowIOValueTypeEnum.string,
              label: 'core.ai.Prompt',
              description: 'core.app.tip.chatNodeSystemPromptTip',
              placeholder: 'core.app.tip.chatNodeSystemPromptTip',
              value: formData.roleDesc
            },
            {
              key: NodeInputKeyEnum.history,
              renderTypeList: [FlowNodeInputTypeEnum.numberInput, FlowNodeInputTypeEnum.reference],
              valueType: WorkflowIOValueTypeEnum.chatHistory,
              label: 'core.module.input.label.chat history',
              required: true,
              min: 0,
              max: 30,
              value: formData.maxHistories
            },
            {
              key: NodeInputKeyEnum.userChatInput,
              renderTypeList: [FlowNodeInputTypeEnum.reference, FlowNodeInputTypeEnum.textarea],
              valueType: WorkflowIOValueTypeEnum.string,
              label: '用户问题',
              required: true,
              toolDescription: '用户问题',
              value: [workflowStartNodeId, 'userChatInput']
            },
            {
              key: NodeInputKeyEnum.aiChatDatasetQuote,
              renderTypeList: [FlowNodeInputTypeEnum.settingDatasetQuotePrompt],
              label: '',
              debugLabel: '知识库引用',
              description: '',
              valueType: WorkflowIOValueTypeEnum.datasetQuote,
              value: ['iKBoX2vIzETU', NodeOutputKeyEnum.codeSearchQuotaQA]
            }
          ],
          outputs: [
            {
              id: 'history',
              key: NodeOutputKeyEnum.history,
              label: 'core.module.output.label.New context',
              description: 'core.module.output.description.New context',
              valueType: WorkflowIOValueTypeEnum.chatHistory,
              type: 'static'
            },
            {
              id: 'answerText',
              key: NodeOutputKeyEnum.answerText,
              label: 'core.module.output.label.Ai response content',
              description: 'core.module.output.description.Ai response content',
              valueType: WorkflowIOValueTypeEnum.string,
              type: 'static'
            }
          ]
        },
        {
          nodeId: 'iKBoX2vIzETU',
          name: '代码搜索',
          intro: '调用“代码搜索”能力，从“Git 仓库”中查找可能与问题相关的参考内容',
          avatar: '/imgs/workflow/db.png',
          flowNodeType: FlowNodeTypeEnum.codeSearch,
          showStatus: true,
          position: {
            x: 918.5901682164496,
            y: -227.11542247619582
          },
          inputs: [
            {
              key: NodeInputKeyEnum.codeSearchSelectList,
              renderTypeList: [FlowNodeInputTypeEnum.selectCodeSearch],
              label: 'core.module.input.label.Select repo',
              value: formData.codeSearch
                ? [
                    {
                      repoName: formData.codeSearch.repoName,
                      commitId: formData.codeSearch.commitId,
                      targetDirectory: formData.codeSearch.targetDirectory
                    }
                  ]
                : [],
              valueType: WorkflowIOValueTypeEnum.selectCodeSearch,
              list: [],
              required: true
            },
            {
              key: NodeInputKeyEnum.userChatInput,
              renderTypeList: [FlowNodeInputTypeEnum.reference, FlowNodeInputTypeEnum.textarea],
              valueType: WorkflowIOValueTypeEnum.string,
              label: '用户问题',
              required: true,
              toolDescription: '需要检索的内容',
              value: [workflowStartNodeId, 'userChatInput']
            }
          ],
          outputs: [
            {
              id: NodeOutputKeyEnum.codeSearchQuotaQA,
              key: NodeOutputKeyEnum.codeSearchQuotaQA,
              label: 'core.module.CodeSearch quote.label',
              type: 'static',
              valueType: WorkflowIOValueTypeEnum.arrayString
            }
          ]
        }
      ],
      edges: [
        {
          source: workflowStartNodeId,
          target: 'iKBoX2vIzETU',
          sourceHandle: `${workflowStartNodeId}-source-right`,
          targetHandle: 'iKBoX2vIzETU-target-left'
        },
        {
          source: 'iKBoX2vIzETU',
          target: '7BdojPlukIQw',
          sourceHandle: 'iKBoX2vIzETU-source-right',
          targetHandle: '7BdojPlukIQw-target-left'
        }
      ]
    };
  }

  function toolTemplates(formData: NormalWorkflowData): WorkflowType {
    const toolNodeId = getNanoid(6);
    const datasetNodeId = getNanoid(6);
    const bodyFields: FlowNodeInputItemType[] = [];

    const datasetTool: WorkflowType | null =
      formData.knowledgeRepoIds.length > 0
        ? {
            nodes: [
              {
                nodeId: datasetNodeId,
                name: '知识库搜索',
                intro: '调用“语义检索”和“全文检索”能力，从“知识库”中查找可能与问题相关的参考内容',
                avatar: '/imgs/workflow/db.png',
                flowNodeType: FlowNodeTypeEnum.datasetSearchNodeV2,
                showStatus: true,
                position: {
                  x: 500,
                  y: 545
                },
                inputs: [
                  {
                    key: NodeInputKeyEnum.datasetSelectListV2,
                    renderTypeList: [FlowNodeInputTypeEnum.selectDataset],
                    label: 'core.module.input.label.Select dataset',
                    value: formData.knowledgeRepoIds.map((k) => ({
                      datasetId: k
                    })),
                    valueType: WorkflowIOValueTypeEnum.selectDatasetV2,
                    list: [],
                    required: true
                  },
                  {
                    key: NodeInputKeyEnum.userChatInput,
                    renderTypeList: [
                      FlowNodeInputTypeEnum.reference,
                      FlowNodeInputTypeEnum.textarea
                    ],
                    valueType: WorkflowIOValueTypeEnum.string,
                    label: '用户问题',
                    required: true,
                    toolDescription: '需要检索的内容',
                    value: ['workflowStartNodeId', 'userChatInput']
                  }
                ],
                outputs: [
                  {
                    id: 'quoteQAV2',
                    key: NodeOutputKeyEnum.datasetQuoteQAV2,
                    label: 'core.module.Dataset quote.label',
                    type: 'static',
                    valueType: WorkflowIOValueTypeEnum.arrayString
                  }
                ]
              }
            ],
            edges: [
              {
                source: datasetNodeId,
                target: toolNodeId,
                sourceHandle: `${datasetNodeId}-source-right`,
                targetHandle: `${toolNodeId}-target-left`
              }
            ]
          }
        : null;

    const tools: WorkflowType[] = [];

    formData.tools.forEach((t, i) => {
      const nodeId = t.code;
      // 记录重名参数数量
      const bodyNameCount = new Map<string, number>();

      const getInputFiled = (rawType: KwaipilotToolInputRawType) => ({
        valueType:
          rawType.type === 'boolean'
            ? WorkflowIOValueTypeEnum.boolean
            : rawType.type === 'number'
              ? WorkflowIOValueTypeEnum.number
              : rawType.type === 'string'
                ? WorkflowIOValueTypeEnum.string
                : WorkflowIOValueTypeEnum.any,
        renderTypeList: [FlowNodeInputTypeEnum.reference],
        key: `${rawType.location}_${rawType.name}`,
        label: `${rawType.location}_${rawType.name}`,
        toolDescription: rawType.description,
        defaultValue: rawType.default,
        required: !!rawType.required,
        canEdit: true,
        editField: {
          key: true,
          description: true
        }
      });

      const { headerTypes, pathTypes, queryTypes, bodyTypes } = getTypesFromSchema({
        schemas: {
          Header: t.enterParamSchema?.headerParamSchema,
          Query: t.enterParamSchema?.queryParamSchema,
          Path: t.enterParamSchema?.pathParamSchema,
          Body: t.enterParamSchema?.bodyParamSchema
        },
        onError: (err, location, schemaStr) => {
          const logPrefix = `tool json schema parse ${location} ${t.tuid} ${t.id} ${t.name}`;
          if (err === KwaipilotToolSchemaParseError.InvalidJSONSchema) {
            logger.error(`${logPrefix} not valid json schema`);
          } else if (err === KwaipilotToolSchemaParseError.InvalidDefinition) {
            logger.error(`${logPrefix} not valid definition`, schemaStr);
          }
          logger.error(`${logPrefix} unknown error`, err);
        },
        valGenerator: (s, name, parentSchema) => {
          const count = (bodyNameCount.get(name) ?? 0) + 1;
          const required = parentSchema?.required?.includes(name) ?? s.required ?? false;

          const inputField = getInputFiled({
            name: count > 1 ? `${name}_${count}` : name,
            location: KwaipilotToolInputRawTypeLocation.Body,
            description: s.description,
            required,
            type: String(s.type).toLowerCase(),
            default: s.default
          });
          bodyNameCount.set(name, count);
          bodyFields.push(inputField);
          return `{{${inputField.key}}}`;
        }
      });

      const queryFields = queryTypes.map((i) => getInputFiled(i));
      const headerFields = headerTypes.map((i) => getInputFiled(i));
      const pathFields = pathTypes.map((i) => getInputFiled(i));

      const nodes: StoreNodeItemType[] = [
        {
          nodeId,
          name: 'Kwaipilot 工具',
          intro: '可以直接使用 Kwaipilot 工具作为其中一个节点',
          avatar:
            'https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/manual-upload/workflow-icons/tool.d9b9c3e52c94b137.svg',
          flowNodeType: FlowNodeTypeEnum.kwaipilotTool,
          showStatus: true,
          position: {
            x: 500 + 500 * (i + 1),
            y: 545
          },
          inputs: [
            {
              key: 'toolSelect',
              renderTypeList: [FlowNodeInputTypeEnum.custom],
              label: '选择工具',
              valueType: WorkflowIOValueTypeEnum.selectTool,
              required: true,
              value: {
                name: t.name,
                desc: t.description,
                icon: t.icon
              }
            },
            // 自定义变量
            ...pathFields,
            ...headerFields,
            ...queryFields,
            ...bodyFields,
            {
              key: NodeInputKeyEnum.addInputParam,
              renderTypeList: [FlowNodeInputTypeEnum.addInputParam],
              valueType: WorkflowIOValueTypeEnum.dynamic,
              label: '',
              required: false,
              description: 'core.module.input.description.HTTP Dynamic Input',
              editField: {
                key: true,
                valueType: true
              }
            },
            // 工具 id
            {
              key: NodeInputKeyEnum.pluginId,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              valueType: WorkflowIOValueTypeEnum.string,
              label: '',
              value: t.tuid,
              required: true
            },
            // 工具版本号
            {
              key: NodeInputKeyEnum.versionId,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              valueType: WorkflowIOValueTypeEnum.number,
              label: '',
              value: t.id,
              required: true
            },
            // 工具唯一标识
            {
              key: NodeInputKeyEnum.identifier,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              valueType: WorkflowIOValueTypeEnum.string,
              label: '',
              value: t.code,
              required: true
            },
            // 工具输入的 json schema（header/body/query）
            {
              key: NodeInputKeyEnum.httpReqJsonSchema,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              valueType: WorkflowIOValueTypeEnum.any,
              value: JSON.stringify(t.enterParamSchema ?? {}),
              label: '',
              required: false
            },
            {
              key: NodeInputKeyEnum.httpMethod,
              renderTypeList: [FlowNodeInputTypeEnum.custom],
              valueType: WorkflowIOValueTypeEnum.string,
              label: '',
              value: t.apiMethod.toUpperCase(),
              required: true
            },
            {
              key: NodeInputKeyEnum.httpPath,
              renderTypeList: [FlowNodeInputTypeEnum.custom],
              valueType: WorkflowIOValueTypeEnum.any,
              value: pathTypes.map((item) => ({
                key: item.name,
                type: item.type,
                value: `{{${KwaipilotToolInputRawTypeLocation.Path}_${item.name}}}`,
                required: item.required
              })),
              label: '',
              description: 'core.module.input.description.Http Request Path',
              placeholder: 'core.module.input.description.Http Request Path',
              required: false
            },
            {
              key: NodeInputKeyEnum.httpHeaders,
              renderTypeList: [FlowNodeInputTypeEnum.custom],
              valueType: WorkflowIOValueTypeEnum.any,
              value: headerTypes.map((item) => ({
                key: item.name,
                type: item.type,
                value: `{{${KwaipilotToolInputRawTypeLocation.Header}_${item.name}}}`,
                required: item.required
              })),
              label: '',
              description: 'core.module.input.description.Http Request Header',
              placeholder: 'core.module.input.description.Http Request Header',
              required: false
            },
            {
              key: NodeInputKeyEnum.httpParams,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              valueType: WorkflowIOValueTypeEnum.any,
              value: queryTypes.map((item) => ({
                key: item.name,
                type: item.type,
                value: `{{${KwaipilotToolInputRawTypeLocation.Query}_${item.name}}}`,
                required: item.required
              })),
              label: '',
              required: false
            },
            {
              key: NodeInputKeyEnum.httpJsonBody,
              renderTypeList: [FlowNodeInputTypeEnum.hidden],
              valueType: WorkflowIOValueTypeEnum.any,
              value: bodyTypes,
              label: '',
              required: false
            }
          ],
          outputs: [
            {
              id: 'httpRawResponse',
              key: NodeOutputKeyEnum.httpRawResponse,
              label: '原始响应',
              description: 'HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。',
              valueType: WorkflowIOValueTypeEnum.any,
              type: 'static'
            }
          ]
        }
      ];

      tools.push({
        nodes: nodes,
        edges: [
          {
            source: toolNodeId,
            target: nodeId,
            sourceHandle: 'selectedTools',
            targetHandle: 'selectedTools'
          }
        ]
      });
    });

    const toolCallNode: StoreNodeItemType = {
      nodeId: toolNodeId,
      name: '工具智能调用',
      intro: '通过AI模型自动选择一个或多个工具进行调用。',
      avatar: '/imgs/workflow/tool.svg',
      flowNodeType: FlowNodeTypeEnum.toolsCall,
      showStatus: true,
      position: {
        x: 1062.1738942532802,
        y: -223.65033022650476
      },
      inputs: [
        {
          key: NodeInputKeyEnum.aiModel,
          renderTypeList: [FlowNodeInputTypeEnum.settingLLMModel, FlowNodeInputTypeEnum.reference],
          label: 'core.module.input.label.aiModel',
          valueType: WorkflowIOValueTypeEnum.string,
          llmModelType: 'all',
          value: formData.model
        },
        {
          key: NodeInputKeyEnum.aiChatTemperature,
          renderTypeList: [FlowNodeInputTypeEnum.hidden],
          label: '',
          value: formData.temperature,
          valueType: WorkflowIOValueTypeEnum.number,
          min: 0,
          max: 10,
          step: 1
        },
        {
          key: NodeInputKeyEnum.aiChatMaxToken,
          renderTypeList: [FlowNodeInputTypeEnum.hidden],
          label: '',
          value: formData.maxToken,
          valueType: WorkflowIOValueTypeEnum.number,
          min: 100,
          max: 4000,
          step: 50
        },
        {
          key: NodeInputKeyEnum.aiSystemPrompt,
          renderTypeList: [FlowNodeInputTypeEnum.textarea, FlowNodeInputTypeEnum.reference],
          max: 3000,
          valueType: WorkflowIOValueTypeEnum.string,
          label: 'core.ai.Prompt',
          description: 'core.app.tip.chatNodeSystemPromptTip',
          placeholder: 'core.app.tip.chatNodeSystemPromptTip',
          value: formData.roleDesc
        },
        {
          key: NodeInputKeyEnum.history,
          renderTypeList: [FlowNodeInputTypeEnum.numberInput, FlowNodeInputTypeEnum.reference],
          valueType: WorkflowIOValueTypeEnum.chatHistory,
          label: 'core.module.input.label.chat history',
          required: true,
          min: 0,
          max: 30,
          value: formData.maxHistories
        },
        {
          key: NodeInputKeyEnum.userChatInput,
          renderTypeList: [FlowNodeInputTypeEnum.reference, FlowNodeInputTypeEnum.textarea],
          valueType: WorkflowIOValueTypeEnum.string,
          label: '用户问题',
          required: true,
          value: [workflowStartNodeId, 'userChatInput']
        }
      ],
      outputs: []
    };

    if (datasetTool) {
      toolCallNode.inputs.push({
        key: NodeInputKeyEnum.aiChatDatasetQuote,
        renderTypeList: [FlowNodeInputTypeEnum.reference],
        label: '知识库引用',
        debugLabel: '知识库引用',
        description: '',
        valueType: WorkflowIOValueTypeEnum.arrayString,
        value: [datasetNodeId, 'quoteQAV2']
      });
    }

    const config: WorkflowType = {
      nodes: [
        toolCallNode,
        // tool nodes
        ...(datasetTool ? datasetTool.nodes : []),
        ...tools.map((tool) => tool.nodes).flat()
      ],
      edges: [
        {
          source: workflowStartNodeId,
          target: datasetTool ? datasetNodeId : toolNodeId,
          sourceHandle: `${workflowStartNodeId}-source-right`,
          targetHandle: `${datasetTool ? datasetNodeId : toolNodeId}-target-left`
        },
        // tool edges
        ...(datasetTool ? datasetTool.edges : []),
        ...tools.map((tool) => tool.edges).flat()
      ]
    };

    return config;
  }

  const workflow = (() => {
    // 如果有代码搜索，优先使用
    if (data.codeSearch) {
      return onlyCodeSearchTemplate(data);
    }
    if (data.tools.length > 0) {
      return toolTemplates(data);
    }
    if (data.knowledgeRepoIds.length > 0) {
      return onlyDatasetTemplate(data);
    }
    return simpleChatTemplate(data);
  })();

  return {
    nodes: [systemConfigTemplate(), workflowStartTemplate(), ...workflow.nodes],
    edges: workflow.edges
  };
}

export const form2ToolDebugWorkflow = (data: ToolDebugWorkflow): WorkflowType => {
  const workflowStartNodeId = 'workflowStartNodeId';
  function systemConfigTemplate(): StoreNodeItemType {
    return {
      nodeId: 'userGuide',
      name: '系统配置',
      intro: '可以配置应用的系统参数',
      flowNodeType: FlowNodeTypeEnum.systemConfig,
      position: {
        x: 531.2422736065552,
        y: -486.7611729549753
      },
      inputs: [
        {
          key: NodeInputKeyEnum.welcomeText,
          renderTypeList: [FlowNodeInputTypeEnum.hidden],
          label: 'core.app.Welcome Text',
          value: ''
        },
        {
          key: NodeInputKeyEnum.recommendedQuestion,
          renderTypeList: [FlowNodeInputTypeEnum.hidden],
          label: '',
          value: []
        }
      ],
      outputs: []
    };
  }
  function workflowStartTemplate(): StoreNodeItemType {
    return {
      nodeId: workflowStartNodeId,
      name: '流程开始',
      intro: '',
      avatar: '/imgs/workflow/userChatInput.svg',
      flowNodeType: FlowNodeTypeEnum.workflowStart,
      position: {
        x: 558.4082376415505,
        y: 123.72387429194112
      },
      inputs: [
        {
          ...Input_Template_UserChatInput,
          toolDescription: '用户问题'
        },
        getUserFilesInputItem()
      ],
      outputs: [
        {
          id: NodeOutputKeyEnum.userChatInput,
          key: NodeOutputKeyEnum.userChatInput,
          label: 'core.module.input.label.user question',
          type: FlowNodeOutputTypeEnum.static,
          valueType: WorkflowIOValueTypeEnum.string
        },
        getUserFilesOutputItem()
      ]
    };
  }

  function toolTemplates(formData: ToolDebugWorkflow): WorkflowType {
    const toolNodeId = getNanoid(6);

    const t = formData.tool;
    const nodeId = t.code;
    // 使用tool 中 Schema 校验数据是否符合

    const getInputFiled = (p: {
      dataStr: string;
      schemaStr: string;
      location: KwaipilotToolInputRawTypeLocation;
    }) => {
      const { dataStr, schemaStr, location } = p;
      const data = JSON.parse(dataStr);
      const { types: rawTypes } = getToolRawTypeFromShallowSchema({ location, schemaStr });

      return Object.keys(data).map((key) => {
        const targetType = rawTypes.find((type) => type.name === key);
        return {
          key,
          require: targetType?.required,
          type: targetType?.type,
          value: data[key]
        };
      });
    };

    const nodes: StoreNodeItemType[] = [
      {
        nodeId,
        name: 'Kwaipilot 工具',
        intro: '可以直接使用 Kwaipilot 工具作为其中一个节点',
        avatar:
          'https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/manual-upload/workflow-icons/tool.d9b9c3e52c94b137.svg',
        flowNodeType: FlowNodeTypeEnum.kwaipilotTool,
        showStatus: true,
        position: {
          x: 500 + 500,
          y: 545
        },
        inputs: [
          {
            key: NodeInputKeyEnum.toolSelect,
            renderTypeList: [FlowNodeInputTypeEnum.custom],
            label: '选择工具',
            valueType: WorkflowIOValueTypeEnum.selectTool,
            required: true,
            value: {
              name: t.name,
              desc: t.description,
              icon: t.icon
            }
          },
          {
            key: NodeInputKeyEnum.addInputParam,
            renderTypeList: [FlowNodeInputTypeEnum.addInputParam],
            valueType: WorkflowIOValueTypeEnum.dynamic,
            label: '',
            required: false,
            description: 'core.module.input.description.HTTP Dynamic Input',
            editField: {
              key: true,
              valueType: true
            }
          },
          // 工具 id
          {
            key: NodeInputKeyEnum.pluginId,
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.string,
            label: '',
            value: t.tuid,
            required: true
          },
          // 工具版本号
          {
            key: NodeInputKeyEnum.versionId,
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.number,
            label: '',
            value: t.id,
            required: true
          },
          // 工具唯一标识
          {
            key: NodeInputKeyEnum.identifier,
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.string,
            label: '',
            value: t.code,
            required: true
          },
          // 工具输入的 json schema（header/body/query）
          {
            key: NodeInputKeyEnum.httpReqJsonSchema,
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.any,
            value: JSON.stringify(t.enterParamSchema ?? {}),
            label: '',
            required: false
          },
          {
            key: NodeInputKeyEnum.httpMethod,
            renderTypeList: [FlowNodeInputTypeEnum.custom],
            valueType: WorkflowIOValueTypeEnum.string,
            label: '',
            value: t.apiMethod.toUpperCase(),
            required: true
          },
          {
            key: NodeInputKeyEnum.httpPath,
            renderTypeList: [FlowNodeInputTypeEnum.custom],
            valueType: WorkflowIOValueTypeEnum.any,
            value:
              formData.reqParam.path && t.enterParamSchema?.pathParamSchema
                ? getInputFiled({
                    dataStr: formData.reqParam.path,
                    schemaStr: t.enterParamSchema?.pathParamSchema,
                    location: KwaipilotToolInputRawTypeLocation.Path
                  })
                : undefined,
            label: '',
            description: 'core.module.input.description.Http Request Path',
            placeholder: 'core.module.input.description.Http Request Path',
            required: false
          },
          {
            key: NodeInputKeyEnum.httpHeaders,
            renderTypeList: [FlowNodeInputTypeEnum.custom],
            valueType: WorkflowIOValueTypeEnum.any,
            value:
              formData.reqParam.header && t.enterParamSchema?.headerParamSchema
                ? getInputFiled({
                    dataStr: formData.reqParam.header,
                    schemaStr: t.enterParamSchema?.headerParamSchema,
                    location: KwaipilotToolInputRawTypeLocation.Header
                  })
                : undefined,
            label: '',
            description: 'core.module.input.description.Http Request Header',
            placeholder: 'core.module.input.description.Http Request Header',
            required: false
          },
          {
            key: NodeInputKeyEnum.httpParams,
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.any,
            value:
              formData.reqParam.query && t.enterParamSchema?.queryParamSchema
                ? getInputFiled({
                    dataStr: formData.reqParam.query,
                    schemaStr: t.enterParamSchema?.queryParamSchema,
                    location: KwaipilotToolInputRawTypeLocation.Query
                  })
                : undefined,
            label: '',
            required: false
          },
          {
            key: NodeInputKeyEnum.httpJsonBody,
            renderTypeList: [FlowNodeInputTypeEnum.hidden],
            valueType: WorkflowIOValueTypeEnum.any,
            value: formData.reqParam.body,
            label: '',
            required: false
          }
        ],
        outputs: [
          {
            id: 'httpRawResponse',
            key: NodeOutputKeyEnum.httpRawResponse,
            label: '原始响应',
            description: 'HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。',
            valueType: WorkflowIOValueTypeEnum.any,
            type: 'static'
          }
        ]
      }
    ];

    const config: WorkflowType = {
      nodes,
      edges: [
        {
          source: workflowStartNodeId,
          target: nodeId,
          sourceHandle: `${workflowStartNodeId}-source-right`,
          targetHandle: `${nodeId}-target-left`
        }
      ]
    };

    return config;
  }

  const workflow = toolTemplates(data);

  return {
    nodes: [systemConfigTemplate(), workflowStartTemplate(), ...workflow.nodes],
    edges: workflow.edges
  };
};
