import type {
  StoreNodeItemType,
  FlowNodeItemType,
  FlowNodeTemplateType
} from '@fastgpt/global/core/workflow/type/index.d';
import type { Edge, Node, XYPosition } from 'reactflow';
import { moduleTemplatesFlat } from '@fastgpt/global/core/workflow/template/constants';
import {
  EDGE_TYPE,
  FlowNodeInputTypeEnum,
  FlowNodeTypeEnum
} from '@fastgpt/global/core/workflow/node/constant';
import { EmptyNode } from '@fastgpt/global/core/workflow/template/system/emptyNode';
import { StoreEdgeItemType } from '@fastgpt/global/core/workflow/type/edge';
import { getNanoid } from '@fastgpt/global/common/string/tools';
import { getGlobalVariableNode } from './adapt';
import { VARIABLE_NODE_ID, WorkflowIOValueTypeEnum } from '@fastgpt/global/core/workflow/constants';
import { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { EditorVariablePickerType } from '@fastgpt/web/components/common/Textarea/PromptEditor/type';
import {
  formatEditorVariablePickerIcon,
  getGuideModule,
  splitGuideModule
} from '@fastgpt/global/core/workflow/utils';
import { getSystemVariables } from '../app/variable';
import { TFunction } from 'next-i18next';
import { checkRequired } from '@fastgpt/global/common/util/json-schema';

export const nodeTemplate2FlowNode = ({
  template,
  position,
  selected
}: {
  template: FlowNodeTemplateType;
  position: XYPosition;
  selected?: boolean;
}): Node<FlowNodeItemType> => {
  // replace item data
  const moduleItem: FlowNodeItemType = {
    ...template,
    nodeId: getNanoid(8)
  };

  return {
    id: moduleItem.nodeId,
    type: moduleItem.flowNodeType,
    data: moduleItem,
    position: position,
    selected
  };
};
export const storeNode2FlowNode = ({
  item: storeNode
}: {
  item: StoreNodeItemType;
}): Node<FlowNodeItemType> => {
  // init some static data
  const template =
    moduleTemplatesFlat.find((template) => template.flowNodeType === storeNode.flowNodeType) ||
    EmptyNode;

  // replace item data
  const moduleItem: FlowNodeItemType = {
    ...template,
    ...storeNode,
    avatar: storeNode?.avatar || template?.avatar,
    inputs: storeNode.inputs
      .map((storeInput) => {
        const templateInput =
          template.inputs.find((item) => item.key === storeInput.key) || storeInput;
        return {
          ...templateInput,
          ...storeInput,
          renderTypeList: templateInput.renderTypeList
        };
      })
      .concat(
        template.inputs.filter((item) => !storeNode.inputs.some((input) => input.key === item.key))
      ),
    outputs: storeNode.outputs.map((storeOutput) => {
      const templateOutput =
        template.outputs.find((item) => item.key === storeOutput.key) || storeOutput;
      return {
        ...templateOutput,
        ...storeOutput,
        value: storeOutput.value
      };
    })
  };

  return {
    id: storeNode.nodeId,
    type: storeNode.flowNodeType,
    data: moduleItem,
    position: storeNode.position || { x: 0, y: 0 }
  };
};
export const storeEdgesRenderEdge = ({ edge }: { edge: StoreEdgeItemType }) => {
  return {
    ...edge,
    id: getNanoid(),
    type: EDGE_TYPE
  };
};

export const computedNodeInputReference = ({
  nodeId,
  nodes,
  edges,
  t
}: {
  nodeId: string;
  nodes: FlowNodeItemType[];
  edges: Edge[];
  t: TFunction;
}) => {
  // get current node
  const node = nodes.find((item) => item.nodeId === nodeId);
  if (!node) {
    return;
  }
  let sourceNodes: FlowNodeItemType[] = [];
  // 根据 edge 获取所有的 source 节点（source节点会继续向前递归获取）
  const findSourceNode = (nodeId: string) => {
    const targetEdges = edges.filter((item) => item.target === nodeId);
    targetEdges.forEach((edge) => {
      const sourceNode = nodes.find((item) => item.nodeId === edge.source);
      if (!sourceNode) return;

      // 去重
      if (sourceNodes.some((item) => item.nodeId === sourceNode.nodeId)) {
        return;
      }
      sourceNodes.push(sourceNode);
      findSourceNode(sourceNode.nodeId);
    });
  };
  findSourceNode(nodeId);

  sourceNodes.unshift(getGlobalVariableNode(nodes, t));

  return sourceNodes;
};

/* Connection rules */
export const checkWorkflowNodeAndConnection = ({
  nodes,
  edges
}: {
  nodes: Node<FlowNodeItemType, string | undefined>[];
  edges: Edge<any>[];
}): string[] | undefined => {
  // 1. reference check. Required value
  for (const node of nodes) {
    const data = node.data;
    const inputs = data.inputs;
    const isToolNode = edges.some(
      (edge) =>
        edge.targetHandle === NodeOutputKeyEnum.selectedTools && edge.target === node.data.nodeId
    );

    if (
      data.flowNodeType === FlowNodeTypeEnum.systemConfig ||
      data.flowNodeType === FlowNodeTypeEnum.pluginInput ||
      data.flowNodeType === FlowNodeTypeEnum.pluginOutput ||
      data.flowNodeType === FlowNodeTypeEnum.workflowStart
    ) {
      continue;
    }

    //针对 Kwaipilot 工具做单独的校验
    if (
      [FlowNodeTypeEnum.httpTool, FlowNodeTypeEnum.kwaipilotTool].includes(
        data.flowNodeType as unknown as FlowNodeTypeEnum
      )
    ) {
      const values = inputs
        .filter((input) => input.value && Array.isArray(input.value))
        .map((input) => input.value)
        .flat();
      if (
        values.some((value) => {
          return value.required && !value.value;
        })
      ) {
        return [data.nodeId];
      }
    }
    if (data.flowNodeType === FlowNodeTypeEnum.kwaipilotTool) {
      // 专门校验新版 Kwaipilot 工具
      const bodyJSONStr = inputs.find(
        (input) => input.key === NodeInputKeyEnum.httpJsonBody
      )?.value;
      const bodyJSONSchemaStr = inputs.find(
        (input) => input.key === NodeInputKeyEnum.httpReqJsonSchema
      )?.value;
      const bodyJSONSchema = JSON.parse(bodyJSONSchemaStr || '{}').bodyParamSchema;
      if (bodyJSONSchema && !checkRequired(bodyJSONSchema, bodyJSONStr)) {
        return [data.nodeId];
      }
    }
    if (
      inputs.some((input) => {
        // check is tool input
        if (isToolNode && input.toolDescription) {
          return false;
        }

        console.log(input, input.required, input.value);
        if (input.required) {
          if (Array.isArray(input.value) && input.value.length === 0) return true;
          if (input.value === undefined || input.value === null) return true;
        }

        // check reference invalid
        const renderType = input.renderTypeList[input.selectedTypeIndex || 0];
        if (renderType === FlowNodeInputTypeEnum.reference && input.required) {
          if (!input.value || !Array.isArray(input.value) || input.value.length !== 2) {
            return true;
          }

          // variable key not need to check
          if (input.value[0] === VARIABLE_NODE_ID) {
            return false;
          }

          // Can not find key
          const sourceNode = nodes.find((item) => item.data.nodeId === input.value[0]);
          if (!sourceNode) {
            return true;
          }
          const sourceOutput = sourceNode.data.outputs.find((item) => item.id === input.value[1]);
          if (!sourceOutput) {
            return true;
          }
        }
        return false;
      })
    ) {
      return [data.nodeId];
    }

    // check empty node(not edge)
    const hasEdge = edges.some(
      (edge) => edge.source === data.nodeId || edge.target === data.nodeId
    );
    if (!hasEdge) {
      return [data.nodeId];
    }
  }
};

export const filterSensitiveNodesData = (nodes: StoreNodeItemType[]) => {
  const cloneNodes = JSON.parse(JSON.stringify(nodes)) as StoreNodeItemType[];

  cloneNodes.forEach((node) => {
    // selected dataset
    if (node.flowNodeType === FlowNodeTypeEnum.datasetSearchNode) {
      node.inputs.forEach((input) => {
        if (input.key === NodeInputKeyEnum.datasetSelectList) {
          input.value = [];
        }
      });
    }

    return node;
  });
  return cloneNodes;
};

/* get workflowStart output to global variables */
export const getWorkflowGlobalVariables = (
  nodes: FlowNodeItemType[],
  t: TFunction
): EditorVariablePickerType[] => {
  const globalVariables = formatEditorVariablePickerIcon(
    splitGuideModule(getGuideModule(nodes))?.variableModules || []
  );

  const systemVariables = getSystemVariables(t);

  return [...globalVariables, ...systemVariables];
};

/**
 * 获取流程图中，所有当前节点前的输出值
 */
export const getWorkflowPreviousVariables = (
  nodes: FlowNodeItemType[],
  edges: Edge<any>[],
  nodeId: string,
  t: TFunction
): EditorVariablePickerType[] => {
  const targetNodeIds: string[] = [nodeId];
  // 存储所有的前序节点
  const previousNodeIds = new Set<string>();

  // 循环判断，找到当前 target node id 的前序节点
  let curTargetNodeId = targetNodeIds.shift();
  while (curTargetNodeId) {
    edges.forEach((e) => {
      // 如果边的 target 节点为当前节点，则将 source 节点加入到 targetNodeIds 中继续搜寻前序节点
      if (e.target === curTargetNodeId) {
        const sourceNodeId = e.source;
        // 如果该节点已经存储到前序节点中，或者就是当前节点，则不需要处理（避免重复或者循环依赖）
        if (previousNodeIds.has(sourceNodeId) || nodeId === sourceNodeId) {
          return;
        }
        targetNodeIds.push(e.source);
        previousNodeIds.add(sourceNodeId);
      }
    });

    curTargetNodeId = targetNodeIds.shift();
  }

  const variables: EditorVariablePickerType[] = [];
  for (let id of previousNodeIds) {
    const node = nodes.find((n) => n.nodeId === id);
    if (node) {
      node.outputs.forEach((v) => {
        // 目前只支持在模版变量中插入这三种基本类型
        if (
          v.valueType === WorkflowIOValueTypeEnum.string ||
          v.valueType === WorkflowIOValueTypeEnum.number ||
          v.valueType === WorkflowIOValueTypeEnum.boolean
        ) {
          variables.push({
            label: `${node.name}/${v.label ? t(v.label) : v.key}`,
            key: `${node.nodeId}/${v.key}`,
            valueType: v.valueType,
            onlyShowLabelInPicker: true
          });
        }
      });
    }
  }

  return variables;
};
