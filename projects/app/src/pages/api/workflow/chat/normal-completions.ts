import type { NextApiRequest, NextApiResponse } from 'next';
import { sseErrRes } from '@fastgpt/service/common/response';
import { NextAPI } from '@/service/middle/entry';
import { form2NormalWorkflow, KwaipilotTool } from '../../../../web/core/app/utils';
import { handleChatCompletion, HistoryMessage } from '../../../../service/core/chat/completion';
import { createLogger, perLogger } from '@fastgpt/global/common/util/logger';
import { getGatewayAppInfoFromRequest, getKSAPAuthnToken } from '../../../../utils/reqInfo';
import { RequestUserChatFile } from '@fastgpt/global/core/chat/type';
import { REQUEST_LIMIT, RESPONSE_LIMIT } from '@/constants/common';

export type RequestData = {
  appId: string;
  username: string;
  model: string;
  prompt: string;
  histories: Array<HistoryMessage>;
  roleDesc: string;
  temperature: number;
  maxHistories: number;
  knowledgeRepoIds: number[];
  tools: KwaipilotTool[];
  topK: number;
  requestId: string;
  variables?: Record<string, any>;
  files?: RequestUserChatFile[];
};

const logger = createLogger('api-workflow-chat-normal-completions');

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // TODO: 不支持 variables？
  const {
    requestId: conversationId,
    appId,
    prompt,
    histories,
    username,
    roleDesc,
    temperature,
    maxHistories,
    model,
    knowledgeRepoIds,
    topK,
    tools = [],
    variables = {},
    files
  } = req.body as RequestData;
  const start = +new Date();
  const gatewayAppInfo = getGatewayAppInfoFromRequest(req);
  try {
    logger.info(`access hit ${conversationId} ${appId} ${username} ${model} ${prompt}`, req.body);
    const { nodes, edges } = form2NormalWorkflow({
      model,
      // NOTE: 相当于默认不设置上限
      maxToken: Infinity,
      roleDesc,
      temperature,
      maxHistories,
      knowledgeRepoIds,
      tools
    });

    await handleChatCompletion(res, {
      conversationId,
      appId,
      prompt,
      histories,
      username,
      variables,
      nodes,
      edges,
      files,
      ksapAuthnToken: getKSAPAuthnToken(req),
      gatewayAppInfo
    });
  } catch (err) {
    perLogger.perf({
      subtag: 'exception',
      millis: +new Date() - start,
      extra1: 'normal-completions',
      extra2: model,
      extra3: gatewayAppInfo.id ? 'gateway' : 'product'
    });
    sseErrRes(res, err);
  } finally {
    res.end();
    logger.info(
      `access end ${conversationId} ${+new Date() - start}ms ${username} ${appId} ${prompt} ${model}`
    );
  }
}
export default NextAPI(handler);

export const config = {
  api: {
    bodyParser: {
      sizeLimit: REQUEST_LIMIT
    },
    responseLimit: RESPONSE_LIMIT
  }
};
