import type { NextApiRequest, NextApiResponse } from 'next';
import { sseErrRes } from '@fastgpt/service/common/response';
import { NextAPI } from '@/service/middle/entry';
import { StoreNodeItemType } from '@fastgpt/global/core/workflow/type';
import { StoreEdgeItemType } from '@fastgpt/global/core/workflow/type/edge';
import { handleChatCompletion, HistoryMessage } from '../../../../service/core/chat/completion';
import { createLogger, perLogger } from '@fastgpt/global/common/util/logger';
import { getGatewayAppInfoFromRequest, getKSAPAuthnToken } from '../../../../utils/reqInfo';
import { RequestUserChatFile } from '@fastgpt/global/core/chat/type';
import { REQUEST_LIMIT, RESPONSE_LIMIT } from '@/constants/common';

export type RequestData = {
  appId: string;
  username: string;
  prompt: string;
  requestId: string;
  historyPrompts?: string[];
  histories: Array<HistoryMessage>;
  flow: {
    nodes: StoreNodeItemType[];
    edges: StoreEdgeItemType[];
  };
  variables?: Record<string, any>;
  files?: RequestUserChatFile[];
};

const logger = createLogger('api-workflow-chat-completions');

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // TODO: 不支持 variables？
  const {
    requestId: conversationId,
    appId,
    flow,
    prompt,
    histories,
    username,
    variables = {},
    files
  } = req.body as RequestData;
  const start = +new Date();
  const gatewayAppInfo = getGatewayAppInfoFromRequest(req);
  try {
    logger.info(`access hit ${conversationId} ${appId} ${username} ${prompt}`, req.body);
    const { nodes, edges } = flow;

    await handleChatCompletion(res, {
      conversationId,
      appId,
      prompt,
      histories,
      username,
      variables,
      nodes,
      edges,
      files,
      ksapAuthnToken: getKSAPAuthnToken(req),
      gatewayAppInfo
    });
  } catch (err) {
    perLogger.perf({
      subtag: 'exception',
      millis: +new Date() - start,
      extra1: 'completions',
      extra3: gatewayAppInfo.id ? 'gateway' : 'product'
    });
    sseErrRes(res, err);
  } finally {
    logger.info(
      `access end ${conversationId} ${+new Date() - start}ms ${username} ${appId} ${prompt}`
    );
    res.end();
  }
}
export default NextAPI(handler);

export const config = {
  api: {
    bodyParser: {
      sizeLimit: REQUEST_LIMIT
    },
    responseLimit: RESPONSE_LIMIT
  }
};
