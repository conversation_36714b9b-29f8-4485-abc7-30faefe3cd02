import type { NextApiRequest, NextApiResponse } from 'next';
import { sseErrRes } from '@fastgpt/service/common/response';
import { NextAPI } from '@/service/middle/entry';
import { form2NormalWorkflow, KwaipilotTool, CodeSearch } from '../../../../web/core/app/utils';
import { handleChatCompletion, HistoryMessage } from '../../../../service/core/chat/completion';
import { createLogger, perLogger } from '@fastgpt/global/common/util/logger';
import { getGatewayAppInfoFromRequest, getKSAPAuthnToken } from '../../../../utils/reqInfo';
import { RequestUserChatFile } from '@fastgpt/global/core/chat/type';
import { REQUEST_LIMIT, RESPONSE_LIMIT } from '@/constants/common';
import { getRagWebSearchToolSystemPrompt } from '@fastgpt/service/common/kconf';

export type RequestData = {
  username: string;
  model: string;
  prompt: string;
  histories: Array<HistoryMessage>; // 历史数据格式见下方
  knowledgeRepoIds?: number[];
  kwaipilotTools?: KwaipilotTool[];
  files?: RequestUserChatFile[];
  codeSearch?: Omit<CodeSearch, 'targetDirectory'> & { targetDirectory?: string[] };
  requestId: string;
};

const logger = createLogger('api-workflow-chat-web-completions');

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // TODO: 不支持 variables？
  const {
    requestId: conversationId,
    prompt,
    histories,
    username,
    model,
    kwaipilotTools = [],
    knowledgeRepoIds = [],
    files,
    codeSearch
  } = req.body as RequestData;
  const start = +new Date();
  const appId = 'chat-engine';
  const gatewayAppInfo = getGatewayAppInfoFromRequest(req);
  try {
    logger.info(`access hit ${conversationId} ${username} ${model} ${prompt}`, req.body);
    // 如果 kwaipilotTools 不为空，则使用 kwaipilotTools 生成 workflow

    const systemPrompt = kwaipilotTools.length > 0 ? await getRagWebSearchToolSystemPrompt() : '';
    logger.info(`systemPrompt: ${systemPrompt}`);
    const { nodes, edges } = form2NormalWorkflow({
      model,
      // NOTE: 相当于默认不设置上限
      maxToken: Infinity,
      roleDesc: systemPrompt,
      // 参照 openai 默认值设置为 1
      // https://platform.openai.com/docs/api-reference/chat/create#chat-create-temperature
      temperature: 1,
      maxHistories: 6,
      knowledgeRepoIds,
      tools: kwaipilotTools,
      codeSearch: codeSearch
        ? {
            targetDirectory: [],
            ...codeSearch
          }
        : undefined
    });

    await handleChatCompletion(res, {
      conversationId,
      appId,
      prompt,
      histories,
      username,
      variables: {},
      nodes,
      edges,
      files,
      ksapAuthnToken: getKSAPAuthnToken(req),
      gatewayAppInfo
    });
  } catch (err) {
    perLogger.perf({
      subtag: 'exception',
      millis: +new Date() - start,
      extra1: 'web-completions',
      extra2: model,
      extra3: gatewayAppInfo.id ? 'gateway' : 'product'
    });
    sseErrRes(res, err);
  } finally {
    res.end();
    logger.info(
      `access end ${conversationId} ${+new Date() - start}ms ${username} ${appId} ${prompt} ${model}`
    );
  }
}
export default NextAPI(handler);

export const config = {
  api: {
    bodyParser: {
      sizeLimit: REQUEST_LIMIT
    },
    responseLimit: RESPONSE_LIMIT
  }
};
