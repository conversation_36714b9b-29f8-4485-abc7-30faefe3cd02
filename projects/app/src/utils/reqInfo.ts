import type { GatewayAppInfo } from '@fastgpt/global/core/workflow/type';
import type { NextApiRequest } from 'next';

const GATEWAY_APP_NAME = 'ai-gateway';

const GatewayHeaders = {
  AppID: `x-${GATEWAY_APP_NAME}-appid`,
  TokenName: `x-${GATEWAY_APP_NAME}-tokenname`,
  TokenOwner: `x-${GATEWAY_APP_NAME}-tokenowner`
} as const;

export const getGatewayAppInfoFromRequest = (req: NextApiRequest): GatewayAppInfo => {
  const id = req.headers[GatewayHeaders.AppID] ?? '';
  const tokenName = req.headers[GatewayHeaders.TokenName] ?? '';
  const tokenOwner = req.headers[GatewayHeaders.TokenOwner] ?? '';
  return {
    id: Array.isArray(id) ? id[0] : id,
    tokenName: Array.isArray(tokenName) ? tokenName[0] : tokenName,
    tokenOwner: Array.isArray(tokenOwner) ? tokenOwner[0] : tokenOwner
  };
};

export const getKSAPAuthnToken = (req: NextApiRequest): string => {
  const tokenHeader = req.headers['x-ksap-authn-token'];
  if (!tokenHeader) {
    return '';
  }

  if (typeof tokenHeader === 'string') {
    return tokenHeader;
  }

  return tokenHeader[0] ?? '';
};

export const getAppInfoFromRequest = (
  req: NextApiRequest
): {
  appId: string;
  conversationId: string;
  username: string;
} => {
  try {
    let appId = req.body?.appId ?? 'unknown';
    if (req.url?.endsWith('/chat/web-completions')) {
      appId = 'web-chat';
    } else if (req.url?.endsWith('/tool/debug')) {
      appId = 'tool-debug';
    }

    const conversationId = req.body?.requestId ?? 'unknown';
    const username = req.body?.username ?? 'anonymous';
    return { appId, conversationId, username };
  } catch (err) {
    return { appId: 'unknown', conversationId: 'unknown', username: 'unknown' };
  }
};
