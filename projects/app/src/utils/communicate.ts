import type { WorkflowAgent } from '@fastgpt/global/core/app/type';

type CommonData = {
  id: number;
  auid: string;
};

type Message = {
  'start-debug': CommonData;
  'update-detail': CommonData & WorkflowAgent;
  'switch-mode': CommonData;
  'flow-click': CommonData;
  'open-debug': null;
  'publish-check-result': CommonData & {
    uuid: string;
    valid: boolean;
  };
};

export function send<T extends keyof Message>(action: T, payload: Message[T]) {
  if (!window.parent) {
    console.debug('No parent window found');
    return;
  }
  const data = {
    action,
    payload
  };
  console.debug('Sending message to parent window', data);
  window.parent.postMessage(data, '*');
}
