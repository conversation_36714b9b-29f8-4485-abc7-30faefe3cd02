import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react';
import {
  Box,
  useTheme,
  useDisclosure,
  Button,
  Menu,
  MenuItem,
  MenuButton,
  MenuList,
  Radio
} from '@chakra-ui/react';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { StoreNodeItemType } from '@fastgpt/global/core/workflow/type/index.d';
import { WorkflowAgent } from '@fastgpt/global/core/app/type.d';
import { useTranslation } from 'next-i18next';
import { useCopyData } from '@/web/common/hooks/useCopyData';
import { AppTypeEnum } from '@fastgpt/global/core/app/constants';
import dynamic from 'next/dynamic';

import { flowNode2StoreNodes } from '@/components/core/workflow/utils';
import { useAppStore } from '@/web/core/app/store/useAppStore';
import { useToast } from '@fastgpt/web/hooks/useToast';
import { useConfirm } from '@fastgpt/web/hooks/useConfirm';
import { StoreEdgeItemType } from '@fastgpt/global/core/workflow/type/edge';
import {
  checkWorkflowNodeAndConnection,
  filterSensitiveNodesData
} from '@/web/core/workflow/utils';
import { useBeforeunload } from '@fastgpt/web/hooks/useBeforeunload';
import { formatTime2HM } from '@fastgpt/global/common/string/time';
import { useContextSelector } from 'use-context-selector';
import { WorkflowContext, getWorkflowStore } from '@/components/core/workflow/context';
import { send } from '@/utils/communicate';
import { EventNameEnum, eventBus } from '@/web/common/utils/eventbus';
import { debounce } from 'lodash';

const ImportSettings = dynamic(() => import('@/components/core/workflow/Flow/ImportSettings'));

type Props = {
  app: WorkflowAgent;
  isOpenTemplate: boolean;
  onCloseTemplate: () => void;
  onOpenTemplate: () => void;
};

const RenderHeaderContainer = React.memo(function RenderHeaderContainer({
  app,
  setWorkflowTestData,
  isOpenTemplate,
  onCloseTemplate,
  onOpenTemplate
}: Props & {
  setWorkflowTestData: React.Dispatch<
    React.SetStateAction<
      | {
          nodes: StoreNodeItemType[];
          edges: StoreEdgeItemType[];
        }
      | undefined
    >
  >;
}) {
  const theme = useTheme();
  const { toast } = useToast();
  const { t } = useTranslation();
  const { copyData } = useCopyData();
  const { openConfirm: openConfigPublish, ConfirmModal } = useConfirm({
    content: t('core.app.Publish Confirm')
  });
  const { updateAppDetail } = useAppStore();
  const edges = useContextSelector(WorkflowContext, (v) => v.edges);

  const [isSaving, setIsSaving] = useState(false);
  const [saveLabel, setSaveLabel] = useState(t('core.app.Onclick to save'));
  const onUpdateNodeError = useContextSelector(WorkflowContext, (v) => v.onUpdateNodeError);

  const { isOpen: isOpenImport, onOpen: onOpenImport, onClose: onCloseImport } = useDisclosure();

  const isShowVersionHistories = useContextSelector(
    WorkflowContext,
    (v) => v.isShowVersionHistories
  );
  const setIsShowVersionHistories = useContextSelector(
    WorkflowContext,
    (v) => v.setIsShowVersionHistories
  );

  const flowData2StoreDataAndCheck = useCallback(async () => {
    const { nodes } = await getWorkflowStore();
    const checkResults = checkWorkflowNodeAndConnection({ nodes, edges });
    if (!checkResults) {
      const storeNodes = flowNode2StoreNodes({ nodes, edges });

      return storeNodes;
    } else {
      checkResults.forEach((nodeId) => onUpdateNodeError(nodeId, true));
      toast({
        status: 'warning',
        title: t('core.workflow.Check Failed')
      });
    }
  }, [edges, onUpdateNodeError, t, toast]);

  const onclickSave = useCallback(async () => {
    if (isShowVersionHistories) return;
    const { nodes } = await getWorkflowStore();

    if (nodes.length === 0) return null;
    setIsSaving(true);

    const storeWorkflow = flowNode2StoreNodes({ nodes, edges });

    try {
      await updateAppDetail(app.id, {
        ...storeWorkflow,
        type: AppTypeEnum.advanced,
        //@ts-ignore
        version: 'v2'
      });

      setSaveLabel(
        t('core.app.Auto Save time', {
          time: formatTime2HM()
        })
      );
      // ChatTestRef.current?.resetChatTest();
    } catch (error) {}

    setIsSaving(false);

    return null;
  }, [isShowVersionHistories, edges, updateAppDetail, app.id, t]);

  const onExportWorkflow = useCallback(async () => {
    const data = await flowData2StoreDataAndCheck();
    if (data) {
      copyData(
        JSON.stringify(
          {
            nodes: filterSensitiveNodesData(data.nodes),
            edges: data.edges
          },
          null,
          2
        ),
        t('app.Export Config Successful')
      );
    }
  }, [copyData, flowData2StoreDataAndCheck, t]);

  const switchMode = useCallback(() => {
    send('switch-mode', { id: app.id, auid: app.auid });
  }, [app.id, app.auid]);

  // effect
  useBeforeunload({
    callback: onclickSave,
    tip: t('core.common.tip.leave page')
  });

  useEffect(() => {
    const fn = debounce(onclickSave, 500, {
      trailing: true,
      leading: false
    });

    eventBus.on(EventNameEnum.updateWorkflowStore, fn);
    return () => {
      eventBus.off(EventNameEnum.updateWorkflowStore);
    };
  }, [onclickSave]);

  useEffect(() => {
    type OnMessage = {
      action: 'publish-check';
      payload: {
        uuid: string;
      };
    };

    const fn = async (event: MessageEvent<OnMessage>) => {
      const { action, payload } = event.data;
      if (action === 'publish-check') {
        const { nodes, edges } = await getWorkflowStore();

        const checkResults = checkWorkflowNodeAndConnection({ nodes, edges });

        if (checkResults) {
          checkResults.forEach((nodeId) => onUpdateNodeError(nodeId, true));

          // 外部 toast
          // toast({
          //   status: 'warning',
          //   title: t('core.workflow.Check Failed')
          // });
        }

        send('publish-check-result', {
          id: app.id,
          auid: app.auid,
          uuid: payload.uuid,
          valid: !checkResults
        });
      }
    };

    window.addEventListener('message', fn);
    return () => {
      window.removeEventListener('message', fn);
    };
  }, [onUpdateNodeError, t, toast]);

  // useInterval(() => {
  //   if (!app.id) return;
  //   onclickSave();
  // }, 20000);

  const Render = useMemo(() => {
    return (
      <>
        <Box
          position={'absolute'}
          top={'16px'}
          left={'0px'}
          right={'0px'}
          display={'flex'}
          alignItems={'center'}
          userSelect={'none'}
          bg={'myGray.25'}
          padding={0}
        >
          <Box position={'absolute'} top={'0px'} left={'24px'} zIndex={1}>
            <Menu>
              {/* // TODO: 切换模式 */}
              <MenuButton
                as={Button}
                rightIcon={<ChevronDownIcon />}
                // onClick={switchMode}
                border="1px solid #D5D6D9"
                backgroundColor="#fff"
                color="#252626"
                borderRadius="6px"
              >
                工作流编排模式
              </MenuButton>
              <MenuList maxW={'420px'}>
                <MenuItem backgroundColor={'#fff'}>
                  <Box
                    display={'flex'}
                    flexDirection={'row'}
                    flex={'1'}
                    justifyContent={'space-between'}
                    border={'1px solid #EBEDF0'}
                    borderRadius={'8px'}
                    p={'12px 16px'}
                    onClick={switchMode}
                  >
                    <Box fontSize={'14px'} lineHeight={'22px'}>
                      <Box color={'#252626'}>简易编排模式</Box>
                      <Box color={'#898A8C'}>构建一个智能Agent，可以自主选择工具来完成任务</Box>
                    </Box>
                    <Radio ml={'12px'}></Radio>
                  </Box>
                </MenuItem>
                <MenuItem backgroundColor={'#fff'}>
                  <Box
                    display={'flex'}
                    flexDirection={'row'}
                    flex={'1'}
                    justifyContent={'space-between'}
                    border={'1px solid #326BFB'}
                    backgroundColor={'#F0F7FF'}
                    borderRadius={'8px'}
                    p={'12px 16px'}
                  >
                    <Box fontSize={'14px'} lineHeight={'22px'}>
                      <Box color={'#252626'}>工作流编排模式</Box>
                      <Box color={'#898A8C'}>
                        以工作流的形式编排生成型应用，提供更多的自定义能力，适合经验丰富的用户。
                      </Box>
                    </Box>
                    <Radio
                      sx={{
                        '--chakra-colors-blue-500': '#326BFB'
                      }}
                      isChecked={true}
                      ml={'12px'}
                    ></Radio>
                  </Box>
                </MenuItem>
              </MenuList>
            </Menu>
          </Box>
          {/* open module template */}
          <Button
            position={'absolute'}
            top={'0px'}
            left={'196px'}
            borderRadius="6px"
            zIndex={1}
            onClick={() => {
              isOpenTemplate ? onCloseTemplate() : onOpenTemplate();
            }}
            leftIcon={
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.74315 2.62788C8.69349 2.2733 8.3797 2 8 2C7.58579 2 7.25 2.32525 7.25 2.72646V7.25H2.72646L2.62788 7.25685C2.2733 7.30651 2 7.6203 2 8C2 8.41421 2.32525 8.75 2.72646 8.75H7.25V13.2735L7.25685 13.3721C7.30651 13.7267 7.6203 14 8 14C8.41421 14 8.75 13.6748 8.75 13.2735V8.75H13.2735L13.3721 8.74315C13.7267 8.69349 14 8.3797 14 8C14 7.58579 13.6748 7.25 13.2735 7.25H8.75V2.72646L8.74315 2.62788Z"
                  fill="white"
                />
              </svg>
            }
          >
            添加节点
          </Button>

          <Button
            position={'absolute'}
            top={'0px'}
            right={'24px'}
            zIndex={1}
            leftIcon={
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8ZM13.5 8C13.5 11.0376 11.0376 13.5 8 13.5C4.96243 13.5 2.5 11.0376 2.5 8C2.5 4.96243 4.96243 2.5 8 2.5C11.0376 2.5 13.5 4.96243 13.5 8ZM9.03848 8L7.45001 6.88807V9.11193L9.03848 8ZM7.38809 5.25287C7.14169 5.23677 6.8956 5.28855 6.67658 5.40258C6.45757 5.51661 6.27402 5.68853 6.14592 5.89962C6.01781 6.11071 5.95005 6.35301 5.95001 6.59994V9.40018C5.95005 9.64711 6.01781 9.88929 6.14592 10.1004C6.27402 10.3115 6.45757 10.4834 6.67658 10.5974C6.8956 10.7115 7.14169 10.7632 7.38809 10.7471C7.63448 10.731 7.87184 10.6476 8.07415 10.506L10.0742 9.10595C10.252 8.9815 10.3972 8.81603 10.4975 8.62355C10.5978 8.43104 10.6501 8.21695 10.6502 7.99988C10.6501 7.78282 10.5978 7.56897 10.4975 7.37645C10.3972 7.18397 10.252 7.01843 10.0742 6.89399L8.07406 5.49392C7.87175 5.35235 7.63449 5.26898 7.38809 5.25287Z"
                  fill="#575859"
                />
              </svg>
            }
            variant={'whitePrimary'}
            onClick={async (e) => {
              const data = await flowData2StoreDataAndCheck();
              if (data) {
                send('start-debug', {
                  id: app.id,
                  auid: app.auid
                });
                setWorkflowTestData(data);
              }
              e.stopPropagation();
            }}
          >
            {t('core.workflow.Debug')}
          </Button>

          {/* <IconButton
            size={'smSquare'}
            icon={<MyIcon name={'common/backFill'} w={'14px'} />}
            borderRadius={'50%'}
            w={'26px'}
            h={'26px'}
            borderColor={'myGray.300'}
            variant={'whiteBase'}
            aria-label={''}
            isLoading={isSaving}
            onClick={saveAndBack}
          />
          <Box ml={[2, 4]}>
            <Box fontSize={['md', 'lg']} fontWeight={'bold'}>
              {app.name}
            </Box>
            {!isShowVersionHistories && (
              <MyTooltip label={t('core.app.Onclick to save')}>
                <Box
                  fontSize={'sm'}
                  mt={1}
                  display={'inline-block'}
                  borderRadius={'xs'}
                  cursor={'pointer'}
                  onClick={onclickSave}
                  color={'myGray.500'}
                >
                  {saveLabel}
                </Box>
              </MyTooltip>
            )}
          </Box>

          <Box flex={1} />

          <MyMenu
            Button={
              <IconButton
                mr={[2, 4]}
                icon={<MyIcon name={'more'} w={'14px'} p={2} />}
                aria-label={''}
                size={'sm'}
                variant={'whitePrimary'}
              />
            }
            menuList={[
              {
                label: t('app.Import Configs'),
                icon: 'common/importLight',
                onClick: onOpenImport
              },
              {
                label: t('app.Export Configs'),
                icon: 'export',
                onClick: onExportWorkflow
              }
            ]}
          />

          <IconButton
            mr={[2, 4]}
            icon={<MyIcon name={'history'} w={'18px'} />}
            aria-label={''}
            size={'sm'}
            w={'30px'}
            variant={'whitePrimary'}
            onClick={() => setIsShowVersionHistories(true)}
          /> */}

          {/* {!isShowVersionHistories && (
            <Button
              ml={[2, 4]}
              size={'sm'}
              isLoading={isSaving}
              leftIcon={<MyIcon name={'common/publishFill'} w={['14px', '16px']} />}
              onClick={openConfigPublish(onclickPublish)}
            >
              {t('core.app.Publish')}
            </Button>
          )} */}
        </Box>
        <ConfirmModal confirmText={t('core.app.Publish')} />
      </>
    );
  }, [
    ConfirmModal,
    app.name,
    flowData2StoreDataAndCheck,
    isSaving,
    onExportWorkflow,
    onOpenImport,
    onclickSave,
    openConfigPublish,
    isShowVersionHistories,
    saveLabel,
    setIsShowVersionHistories,
    setWorkflowTestData,
    t,
    theme.borders.base,
    isOpenTemplate
  ]);

  return (
    <>
      {Render}
      {isOpenImport && <ImportSettings onClose={onCloseImport} />}
      {/* {isShowVersionHistories && <PublishHistories />} */}
    </>
  );
});

const Header = (props: Props) => {
  const [workflowTestData, setWorkflowTestData] = useState<{
    nodes: StoreNodeItemType[];
    edges: StoreEdgeItemType[];
  }>();

  return (
    <>
      <RenderHeaderContainer {...props} setWorkflowTestData={setWorkflowTestData} />
    </>
  );
};

export default React.memo(Header);
