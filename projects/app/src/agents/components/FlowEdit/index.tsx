import React, { useEffect, useMemo } from 'react';
import { WorkflowAgent } from '@fastgpt/global/core/app/type.d';
import Header from './Header';
import Flow from '@/components/core/workflow/Flow';
import { appSystemModuleTemplates } from '@fastgpt/global/core/workflow/template/constants';
import WorkflowContextProvider, { WorkflowContext } from '@/components/core/workflow/context';
import { useContextSelector } from 'use-context-selector';
import { useDisclosure } from '@chakra-ui/react';

type Props = { app: WorkflowAgent };

const Render = ({ app }: Props) => {
  const {
    isOpen: isOpenTemplate,
    onClose: onCloseTemplate,
    onOpen: onOpenTemplate
  } = useDisclosure();
  const initData = useContextSelector(WorkflowContext, (v) => v.initData);

  const workflowStringData = JSON.stringify({
    nodes: app.workFlow.nodes || [],
    edges: app.workFlow.edges || []
  });

  useEffect(() => {
    initData(JSON.parse(workflowStringData));
  }, [initData, workflowStringData]);

  const memoRender = useMemo(() => {
    return (
      <Flow
        isOpenTemplate={isOpenTemplate}
        onCloseTemplate={onCloseTemplate}
        onOpenTemplate={onOpenTemplate}
        Header={
          <Header
            app={app}
            onOpenTemplate={onOpenTemplate}
            isOpenTemplate={isOpenTemplate}
            onCloseTemplate={onCloseTemplate}
          />
        }
        auid={app.auid}
        id={app.id}
      />
    );
  }, [app, isOpenTemplate, onCloseTemplate, onOpenTemplate]);

  return <>{memoRender}</>;
};

export default React.memo(function FlowEdit(props: Props) {
  const filterAppIds = useMemo(() => [props.app.auid], [props.app.auid]);

  return (
    <WorkflowContextProvider
      value={{
        appId: props.app.auid,
        mode: 'app',
        filterAppIds,
        basicNodeTemplates: appSystemModuleTemplates
      }}
    >
      <Render {...props} />
    </WorkflowContextProvider>
  );
});
