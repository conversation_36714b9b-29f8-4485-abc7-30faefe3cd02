// @ts-nocheck

export const iconPaths = {
  change: () => import('./icons/change.svg'),
  chatSend: () => import('./icons/chatSend.svg'),
  closeSolid: () => import('./icons/closeSolid.svg'),
  collectionLight: () => import('./icons/collectionLight.svg'),
  collectionSolid: () => import('./icons/collectionSolid.svg'),
  'common/addCircleLight': () => import('./icons/common/addCircleLight.svg'),
  'common/addLight': () => import('./icons/common/addLight.svg'),
  'common/backFill': () => import('./icons/common/backFill.svg'),
  'common/backLight': () => import('./icons/common/backLight.svg'),
  'common/clearLight': () => import('./icons/common/clearLight.svg'),
  'common/closeLight': () => import('./icons/common/closeLight.svg'),
  'common/confirm/commonTip': () => import('./icons/common/confirm/commonTip.svg'),
  'common/confirm/deleteTip': () => import('./icons/common/confirm/deleteTip.svg'),
  'common/confirm/rightTip': () => import('./icons/common/confirm/rightTip.svg'),
  'common/courseLight': () => import('./icons/common/courseLight.svg'),
  'common/customTitleLight': () => import('./icons/common/customTitleLight.svg'),
  'common/data': () => import('./icons/common/data.svg'),
  'common/editor/resizer': () => import('./icons/common/editor/resizer.svg'),
  'common/errorFill': () => import('./icons/common/errorFill.svg'),
  'common/file/move': () => import('./icons/common/file/move.svg'),
  'common/folderFill': () => import('./icons/common/folderFill.svg'),
  'common/fullScreenLight': () => import('./icons/common/fullScreenLight.svg'),
  'common/gitFill': () => import('./icons/common/gitFill.svg'),
  'common/gitInlight': () => import('./icons/common/gitInlight.svg'),
  'common/gitLight': () => import('./icons/common/gitLight.svg'),
  'common/googleFill': () => import('./icons/common/googleFill.svg'),
  'common/importLight': () => import('./icons/common/importLight.svg'),
  'common/inviteLight': () => import('./icons/common/inviteLight.svg'),
  'common/language/en': () => import('./icons/common/language/en.svg'),
  'common/language/zh': () => import('./icons/common/language/zh.svg'),
  'common/linkBlue': () => import('./icons/common/linkBlue.svg'),
  'common/loading': () => import('./icons/common/loading.svg'),
  'common/navbar/pluginFill': () => import('./icons/common/navbar/pluginFill.svg'),
  'common/navbar/pluginLight': () => import('./icons/common/navbar/pluginLight.svg'),
  'common/openai': () => import('./icons/common/openai.svg'),
  'common/overviewLight': () => import('./icons/common/overviewLight.svg'),
  'common/paramsLight': () => import('./icons/common/paramsLight.svg'),
  'common/playFill': () => import('./icons/common/playFill.svg'),
  'common/playLight': () => import('./icons/common/playLight.svg'),
  'common/publishFill': () => import('./icons/common/publishFill.svg'),
  'common/questionLight': () => import('./icons/common/questionLight.svg'),
  'common/refreshLight': () => import('./icons/common/refreshLight.svg'),
  'common/resultLight': () => import('./icons/common/resultLight.svg'),
  'common/retryLight': () => import('./icons/common/retryLight.svg'),
  'common/rightArrowLight': () => import('./icons/common/rightArrowLight.svg'),
  'common/routePushLight': () => import('./icons/common/routePushLight.svg'),
  'common/saveFill': () => import('./icons/common/saveFill.svg'),
  'common/searchLight': () => import('./icons/common/searchLight.svg'),
  'common/selectLight': () => import('./icons/common/selectLight.svg'),
  'common/settingLight': () => import('./icons/common/settingLight.svg'),
  'common/text/t': () => import('./icons/common/text/t.svg'),
  'common/tickFill': () => import('./icons/common/tickFill.svg'),
  'common/uploadFileFill': () => import('./icons/common/uploadFileFill.svg'),
  'common/viewLight': () => import('./icons/common/viewLight.svg'),
  'common/voiceLight': () => import('./icons/common/voiceLight.svg'),
  'common/wechatFill': () => import('./icons/common/wechatFill.svg'),
  copy: () => import('./icons/copy.svg'),
  'core/app/aiFill': () => import('./icons/core/app/aiFill.svg'),
  'core/app/aiLight': () => import('./icons/core/app/aiLight.svg'),
  'core/app/appApiLight': () => import('./icons/core/app/appApiLight.svg'),
  'core/app/customFeedback': () => import('./icons/core/app/customFeedback.svg'),
  'core/app/headphones': () => import('./icons/core/app/headphones.svg'),
  'core/app/logsLight': () => import('./icons/core/app/logsLight.svg'),
  'core/app/markLight': () => import('./icons/core/app/markLight.svg'),
  'core/app/publish/lark': () => import('./icons/core/app/publish/lark.svg'),
  'core/app/questionGuide': () => import('./icons/core/app/questionGuide.svg'),
  'core/app/schedulePlan': () => import('./icons/core/app/schedulePlan.svg'),
  'core/app/simpleMode/ai': () => import('./icons/core/app/simpleMode/ai.svg'),
  'core/app/simpleMode/chat': () => import('./icons/core/app/simpleMode/chat.svg'),
  'core/app/simpleMode/dataset': () => import('./icons/core/app/simpleMode/dataset.svg'),
  'core/app/simpleMode/template': () => import('./icons/core/app/simpleMode/template.svg'),
  'core/app/simpleMode/tts': () => import('./icons/core/app/simpleMode/tts.svg'),
  'core/app/simpleMode/variable': () => import('./icons/core/app/simpleMode/variable.svg'),
  'core/app/simpleMode/whisper': () => import('./icons/core/app/simpleMode/whisper.svg'),
  'core/app/toolCall': () => import('./icons/core/app/toolCall.svg'),
  'core/app/ttsFill': () => import('./icons/core/app/ttsFill.svg'),
  'core/app/variable/external': () => import('./icons/core/app/variable/external.svg'),
  'core/app/variable/input': () => import('./icons/core/app/variable/input.svg'),
  'core/app/variable/select': () => import('./icons/core/app/variable/select.svg'),
  'core/app/variable/textarea': () => import('./icons/core/app/variable/textarea.svg'),
  'core/chat/QGFill': () => import('./icons/core/chat/QGFill.svg'),
  'core/chat/cancelSpeak': () => import('./icons/core/chat/cancelSpeak.svg'),
  'core/chat/chatFill': () => import('./icons/core/chat/chatFill.svg'),
  'core/chat/chatLight': () => import('./icons/core/chat/chatLight.svg'),
  'core/chat/chatModelTag': () => import('./icons/core/chat/chatModelTag.svg'),
  'core/chat/feedback/badLight': () => import('./icons/core/chat/feedback/badLight.svg'),
  'core/chat/feedback/goodLight': () => import('./icons/core/chat/feedback/goodLight.svg'),
  'core/chat/fileSelect': () => import('./icons/core/chat/fileSelect.svg'),
  'core/chat/finishSpeak': () => import('./icons/core/chat/finishSpeak.svg'),
  'core/chat/quoteFill': () => import('./icons/core/chat/quoteFill.svg'),
  'core/chat/quoteSign': () => import('./icons/core/chat/quoteSign.svg'),
  'core/chat/recordFill': () => import('./icons/core/chat/recordFill.svg'),
  'core/chat/sendFill': () => import('./icons/core/chat/sendFill.svg'),
  'core/chat/sendLight': () => import('./icons/core/chat/sendLight.svg'),
  'core/chat/setTopLight': () => import('./icons/core/chat/setTopLight.svg'),
  'core/chat/speaking': () => import('./icons/core/chat/speaking.svg'),
  'core/chat/stopSpeech': () => import('./icons/core/chat/stopSpeech.svg'),
  'core/dataset/commonDataset': () => import('./icons/core/dataset/commonDataset.svg'),
  'core/dataset/datasetFill': () => import('./icons/core/dataset/datasetFill.svg'),
  'core/dataset/datasetLight': () => import('./icons/core/dataset/datasetLight.svg'),
  'core/dataset/fileCollection': () => import('./icons/core/dataset/fileCollection.svg'),
  'core/dataset/fullTextRecall': () => import('./icons/core/dataset/fullTextRecall.svg'),
  'core/dataset/manualCollection': () => import('./icons/core/dataset/manualCollection.svg'),
  'core/dataset/mixedRecall': () => import('./icons/core/dataset/mixedRecall.svg'),
  'core/dataset/modeEmbedding': () => import('./icons/core/dataset/modeEmbedding.svg'),
  'core/dataset/rerank': () => import('./icons/core/dataset/rerank.svg'),
  'core/dataset/splitLight': () => import('./icons/core/dataset/splitLight.svg'),
  'core/dataset/tableCollection': () => import('./icons/core/dataset/tableCollection.svg'),
  'core/dataset/websiteDataset': () => import('./icons/core/dataset/websiteDataset.svg'),
  'core/modules/basicNode': () => import('./icons/core/modules/basicNode.svg'),
  'core/modules/fixview': () => import('./icons/core/modules/fixview.svg'),
  'core/modules/flowLight': () => import('./icons/core/modules/flowLight.svg'),
  'core/modules/previewLight': () => import('./icons/core/modules/previewLight.svg'),
  'core/modules/systemPlugin': () => import('./icons/core/modules/systemPlugin.svg'),
  'core/modules/teamPlugin': () => import('./icons/core/modules/teamPlugin.svg'),
  'core/modules/variable': () => import('./icons/core/modules/variable.svg'),
  'core/modules/welcomeText': () => import('./icons/core/modules/welcomeText.svg'),
  'core/workflow/closeEdge': () => import('./icons/core/workflow/closeEdge.svg'),
  'core/workflow/debug': () => import('./icons/core/workflow/debug.svg'),
  'core/workflow/debugBlue': () => import('./icons/core/workflow/debugBlue.svg'),
  'core/workflow/debugNext': () => import('./icons/core/workflow/debugNext.svg'),
  'core/workflow/debugResult': () => import('./icons/core/workflow/debugResult.svg'),
  'core/workflow/edgeArrow': () => import('./icons/core/workflow/edgeArrow.svg'),
  'core/workflow/grout': () => import('./icons/core/workflow/grout.svg'),
  'core/workflow/inputType/dynamic': () => import('./icons/core/workflow/inputType/dynamic.svg'),
  'core/workflow/inputType/input': () => import('./icons/core/workflow/inputType/input.svg'),
  'core/workflow/inputType/jsonEditor': () =>
    import('./icons/core/workflow/inputType/jsonEditor.svg'),
  'core/workflow/inputType/numberInput': () =>
    import('./icons/core/workflow/inputType/numberInput.svg'),
  'core/workflow/inputType/reference': () =>
    import('./icons/core/workflow/inputType/reference.svg'),
  'core/workflow/inputType/select': () => import('./icons/core/workflow/inputType/select.svg'),
  'core/workflow/inputType/selectApp': () =>
    import('./icons/core/workflow/inputType/selectApp.svg'),
  'core/workflow/inputType/selectDataset': () =>
    import('./icons/core/workflow/inputType/selectDataset.svg'),
  'core/workflow/inputType/selectLLM': () =>
    import('./icons/core/workflow/inputType/selectLLM.svg'),
  'core/workflow/inputType/switch': () => import('./icons/core/workflow/inputType/switch.svg'),
  'core/workflow/inputType/textarea': () => import('./icons/core/workflow/inputType/textarea.svg'),
  'core/workflow/revertVersion': () => import('./icons/core/workflow/revertVersion.svg'),
  'core/workflow/runError': () => import('./icons/core/workflow/runError.svg'),
  'core/workflow/runSkip': () => import('./icons/core/workflow/runSkip.svg'),
  'core/workflow/runSuccess': () => import('./icons/core/workflow/runSuccess.svg'),
  'core/workflow/running': () => import('./icons/core/workflow/running.svg'),
  'core/workflow/versionHistories': () => import('./icons/core/workflow/versionHistories.svg'),
  date: () => import('./icons/date.svg'),
  delete: () => import('./icons/delete.svg'),
  edit: () => import('./icons/edit.svg'),
  empty: () => import('./icons/empty.svg'),
  export: () => import('./icons/export.svg'),
  'file/csv': () => import('./icons/file/csv.svg'),
  'file/fill/csv': () => import('./icons/file/fill/csv.svg'),
  'file/fill/doc': () => import('./icons/file/fill/doc.svg'),
  'file/fill/file': () => import('./icons/file/fill/file.svg'),
  'file/fill/folder': () => import('./icons/file/fill/folder.svg'),
  'file/fill/html': () => import('./icons/file/fill/html.svg'),
  'file/fill/manual': () => import('./icons/file/fill/manual.svg'),
  'file/fill/markdown': () => import('./icons/file/fill/markdown.svg'),
  'file/fill/pdf': () => import('./icons/file/fill/pdf.svg'),
  'file/fill/ppt': () => import('./icons/file/fill/ppt.svg'),
  'file/fill/txt': () => import('./icons/file/fill/txt.svg'),
  'file/fill/xlsx': () => import('./icons/file/fill/xlsx.svg'),
  'file/html': () => import('./icons/file/html.svg'),
  'file/indexImport': () => import('./icons/file/indexImport.svg'),
  'file/manualImport': () => import('./icons/file/manualImport.svg'),
  'file/markdown': () => import('./icons/file/markdown.svg'),
  'file/pdf': () => import('./icons/file/pdf.svg'),
  'file/qaImport': () => import('./icons/file/qaImport.svg'),
  'file/uploadFile': () => import('./icons/file/uploadFile.svg'),
  history: () => import('./icons/history.svg'),
  kbTest: () => import('./icons/kbTest.svg'),
  menu: () => import('./icons/menu.svg'),
  minus: () => import('./icons/minus.svg'),
  'modal/concat': () => import('./icons/modal/concat.svg'),
  'modal/confirmPay': () => import('./icons/modal/confirmPay.svg'),
  'modal/edit': () => import('./icons/modal/edit.svg'),
  'modal/manualDataset': () => import('./icons/modal/manualDataset.svg'),
  'modal/selectSource': () => import('./icons/modal/selectSource.svg'),
  'modal/setting': () => import('./icons/modal/setting.svg'),
  'modal/teamPlans': () => import('./icons/modal/teamPlans.svg'),
  more: () => import('./icons/more.svg'),
  out: () => import('./icons/out.svg'),
  'phoneTabbar/me': () => import('./icons/phoneTabbar/me.svg'),
  'phoneTabbar/tool': () => import('./icons/phoneTabbar/tool.svg'),
  'phoneTabbar/toolFill': () => import('./icons/phoneTabbar/toolFill.svg'),
  'price/bg': () => import('./icons/price/bg.svg'),
  'price/right': () => import('./icons/price/right.svg'),
  save: () => import('./icons/save.svg'),
  stop: () => import('./icons/stop.svg'),
  'support/account/loginoutLight': () => import('./icons/support/account/loginoutLight.svg'),
  'support/account/passwordLogin': () => import('./icons/support/account/passwordLogin.svg'),
  'support/account/plans': () => import('./icons/support/account/plans.svg'),
  'support/account/promotionLight': () => import('./icons/support/account/promotionLight.svg'),
  'support/bill/extraDatasetsize': () => import('./icons/support/bill/extraDatasetsize.svg'),
  'support/bill/extraPoints': () => import('./icons/support/bill/extraPoints.svg'),
  'support/bill/payRecordLight': () => import('./icons/support/bill/payRecordLight.svg'),
  'support/bill/priceLight': () => import('./icons/support/bill/priceLight.svg'),
  'support/bill/shoppingCart': () => import('./icons/support/bill/shoppingCart.svg'),
  'support/outlink/apikeyFill': () => import('./icons/support/outlink/apikeyFill.svg'),
  'support/outlink/apikeyLight': () => import('./icons/support/outlink/apikeyLight.svg'),
  'support/outlink/iframeLight': () => import('./icons/support/outlink/iframeLight.svg'),
  'support/outlink/share': () => import('./icons/support/outlink/share.svg'),
  'support/outlink/shareLight': () => import('./icons/support/outlink/shareLight.svg'),
  'support/permission/privateLight': () => import('./icons/support/permission/privateLight.svg'),
  'support/permission/publicLight': () => import('./icons/support/permission/publicLight.svg'),
  'support/team/memberLight': () => import('./icons/support/team/memberLight.svg'),
  'support/usage/usageRecordLight': () => import('./icons/support/usage/usageRecordLight.svg'),
  'support/user/individuation': () => import('./icons/support/user/individuation.svg'),
  'support/user/informLight': () => import('./icons/support/user/informLight.svg'),
  'support/user/userFill': () => import('./icons/support/user/userFill.svg'),
  'support/user/userLight': () => import('./icons/support/user/userLight.svg'),
  text: () => import('./icons/text.svg'),
  user: () => import('./icons/user.svg'),
  wx: () => import('./icons/wx.svg')
};
