import type {
  ArtifactChatItem,
  ChatItemType,
  UserChatFile,
  UserChatItemValueItemType
} from '@fastgpt/global/core/chat/type.d';
import {
  WorkflowIOValueTypeEnum,
  NodeOutputKeyEnum
} from '@fastgpt/global/core/workflow/constants';
import {
  codeJointTemplate,
  fileJointTemplate,
  docJointTemplate
} from '@fastgpt/global/core/workflow/utils';
import { RuntimeEdgeItemType } from '@fastgpt/global/core/workflow/runtime/type';
import { LLMModelItemType } from '@fastgpt/global/core/ai/model.d';
import { replaceVariable } from '@fastgpt/global/common/string/tools';
import {
  chats2GPTMessages,
  getSystemPrompt,
  runtimePrompt2ChatsValue
} from '@fastgpt/global/core/chat/adapt';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum,
  IS_OVERSEA
} from '@fastgpt/global/core/chat/constants';
import { formatGPTMessagesInRequestBefore, loadChatImgToBase64 } from '../../../core/chat/utils';
import {
  filterSearchResultsByMaxChars2,
  getQuoteUserPromptFromKconf,
  getQuoteSystemPromptFromKconf
} from '../utils';
import { countMessagesTokens } from '../../../common/string/tiktoken';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { ChatCompletionRequestMessageRoleEnum } from '@fastgpt/global/core/ai/constants';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { ragSearch, RagSearchFile, RagWebSearchItem } from '../../../common/http/ragSearch';
import { InternalRagSearchType } from '@fastgpt/global/core/dataset/constants';
import { responseWrite } from '../../../common/response';

import type { DatasetCodeSearchResult } from '@fastgpt/global/core/dataset/type';
import type { QuoteType } from '../../../common/kconf';
import type { DispatchEvenEmitter } from '@fastgpt/global/core/workflow/type';
import type { QuoteSource } from './type';
import type { DatasetSearchResult } from './dataset/type';

const logger = createLogger('workflow-dispatch-utils');

export const filterToolNodeIdByEdges = ({
  nodeId,
  edges
}: {
  nodeId: string;
  edges: RuntimeEdgeItemType[];
}) => {
  return edges
    .filter(
      (edge) => edge.source === nodeId && edge.targetHandle === NodeOutputKeyEnum.selectedTools
    )
    .map((edge) => edge.target);
};

// export const checkTheModuleConnectedByTool = (
//   modules: StoreNodeItemType[],
//   node: StoreNodeItemType
// ) => {
//   let sign = false;
//   const toolModules = modules.filter((item) => item.flowNodeType === FlowNodeTypeEnum.tools);

//   toolModules.forEach((item) => {
//     const toolOutput = item.outputs.find(
//       (output) => output.key === NodeOutputKeyEnum.selectedTools
//     );
//     toolOutput?.targets.forEach((target) => {
//       if (target.moduleId === node.moduleId) {
//         sign = true;
//       }
//     });
//   });

//   return sign;
// };

export const getHistories = (history?: ChatItemType[] | number, histories: ChatItemType[] = []) => {
  if (!history) return [];
  if (typeof history === 'number') return histories.slice(-history * 2);
  if (Array.isArray(history)) return history;

  return [];
};

/* value type format */
export const valueTypeFormat = (value: any, type?: WorkflowIOValueTypeEnum) => {
  if (value === undefined) return;

  if (type === 'string') {
    if (typeof value !== 'object') return String(value);
    return JSON.stringify(value);
  }
  if (type === 'number') return Number(value);
  if (type === 'boolean') return Boolean(value);
  try {
    if (type === WorkflowIOValueTypeEnum.datasetQuote && !Array.isArray(value)) {
      return JSON.parse(value);
    }
    if (type === WorkflowIOValueTypeEnum.selectDataset && !Array.isArray(value)) {
      return JSON.parse(value);
    }
  } catch (error) {
    return value;
  }

  return value;
};

async function combineUserInputWithQuote({
  userChatInput,
  inputFiles,
  quotePrompt,
  quoteText,
  quoteType,
  useDirectUserInput,
  repoName = ''
}: {
  userChatInput: string;
  inputFiles: UserChatItemValueItemType['file'][];
  quoteText: string;
  quoteType: QuoteType;
  useDirectUserInput: boolean;
  quotePrompt?: string;
  repoName?: string;
}): Promise<ChatItemType> {
  const replaceInputValue =
    quoteText !== undefined && !useDirectUserInput
      ? replaceVariable(quotePrompt || (await getQuoteUserPromptFromKconf(quoteType)), {
          quote: quoteText,
          question: userChatInput,
          repoName
        })
      : userChatInput;

  return {
    obj: ChatRoleEnum.Human,
    value: runtimePrompt2ChatsValue({
      files: inputFiles,
      text: replaceInputValue
    })
  };
}

async function filterQuote({
  userChatInput,
  quoteQA = [],
  maxTokens,
  quoteSeparator,
  quoteType
}: {
  userChatInput: string;
  maxTokens: number;
  quoteType: QuoteType;
  quoteSeparator: string;
  quoteQA?: string[];
}) {
  const userInputTokens = await countMessagesTokens([
    {
      obj: ChatRoleEnum.Human,
      value: runtimePrompt2ChatsValue({
        files: [],
        text: userChatInput
      })
    }
  ]);
  const leftTokens = maxTokens - userInputTokens;
  if (leftTokens <= 0) {
    return {
      quoteText: ''
    };
  }

  // slice filterSearch
  const filterQuoteQA = await filterSearchResultsByMaxChars2(quoteQA, leftTokens, quoteType);

  // const quoteText =
  //   filterQuoteQA.length > 0
  //     ? `${filterQuoteQA.map((item, index) => getValue(item, index).trim()).join('\n------\n')}`
  //     : '';

  const quoteText = filterQuoteQA.join(quoteSeparator);

  return {
    quoteText
  };
}

function formatQuotaQA(quoteQA: unknown) {
  const quotaList: unknown[] = [];
  if (quoteQA) {
    if (Array.isArray(quoteQA)) {
      quoteQA.forEach((q) => {
        quotaList.push(q);
      });
    } else {
      quotaList.push(quoteQA);
    }
  }
  return quotaList;
}

function convert2QuoteQAStr(it: unknown, index: number, advancedQuote: boolean) {
  const num = index + 1;
  if (isCodeRagSearchResult(it)) {
    return codeJointTemplate(num, it);
  }
  if (isDocsRagSearchResult(it)) {
    return advancedQuote
      ? docJointTemplate(num, it)
      : JSON.stringify({
          序号: index,
          来源: `[${it.title}](${it.link})`,
          内容: it.text
        });
  }
  return typeof it === 'string' ? it : JSON.stringify(it);
}

export const isCodeRagSearchResult = (it: unknown): it is DatasetCodeSearchResult => {
  return (
    typeof it === 'object' && !!it && '__type__' in it && it.__type__ === InternalRagSearchType.code
  );
};

export const isDocsRagSearchResult = (it: unknown): it is DatasetSearchResult => {
  return (
    typeof it === 'object' && !!it && '__type__' in it && it.__type__ === InternalRagSearchType.docs
  );
};

export async function getFormattedGPTMessages({
  history,
  quoteQAOriginal,
  histories: historiesOriginal,
  userChatInput,
  model,
  inputFiles,
  systemPrompt,
  useInputFiles,
  advancedQuote,
  maxTokens,
  quotePrompt
}: {
  history: number | ChatItemType[];
  quoteQAOriginal: unknown;
  histories: ChatItemType[];
  userChatInput: string;
  model: LLMModelItemType;
  inputFiles: UserChatFile[];
  systemPrompt: string;
  useInputFiles: boolean;
  advancedQuote: boolean;
  maxTokens: number;
  quotePrompt?: string;
}) {
  logger.info(`systemPrompt1: ${systemPrompt}`);

  let useDirectUserInput = !quoteQAOriginal;
  const quoteQAList = formatQuotaQA(quoteQAOriginal);
  const histories = getHistories(history, historiesOriginal);
  const availableInputFiles: UserChatFile[] = [];
  inputFiles.forEach((file) => {
    if (file.type === ChatFileTypeEnum.file && file.content) {
      availableInputFiles.push(file);
    }
  });

  histories.forEach((item) => {
    if (item.obj === ChatRoleEnum.Human) {
      item.value.forEach((v) => {
        if (v.type === ChatItemValueTypeEnum.file && v.file && v.file.content) {
          availableInputFiles.push(v.file);
        }
      });
    }
  });

  const quoteQA: string[] = [];
  let repoName = '';
  let quoteType = (advancedQuote ? 'docs' : 'simple') as QuoteType;
  const ragFiles: RagSearchFile[] = [];
  const ragSearchResult: (RagWebSearchItem & {
    filename: string;
    fileUrl: string;
  })[] = [];
  // 只有开启了增强知识引用，并且没有其他知识引用的情况下，才会使用文件 RAG
  const enableRagForInputFiles = advancedQuote && quoteQAList.length === 0;
  if (enableRagForInputFiles) {
    availableInputFiles.forEach((file) => {
      // 如果没有分页信息，则将整个文件作为一个分页
      if (file.pages.length === 0 && file.content) {
        ragFiles.push({
          filename: file.name,
          fileIndex: file.index,
          pageNumber: 1,
          content: file.content
        });
        return;
      }

      // 如果有分页信息，则将每个分页作为一个 RagSearchFile
      file.pages.forEach((page) => {
        if (page.content) {
          ragFiles.push({
            filename: file.name,
            fileIndex: file.index,
            pageNumber: page.pageNumber,
            content: page.content
          });
        }
      });
    });

    if (ragFiles.length > 0) {
      const ragResult = await ragSearch(userChatInput, ragFiles);
      logger.info(`ragSearchResult ${JSON.stringify(ragResult)}`);

      if (ragResult.status === 'success' && ragResult.results.length > 0) {
        ragResult.results.forEach((it, idx) => {
          const num = idx + 1;
          quoteQA.push(fileJointTemplate(num, it));

          const file = availableInputFiles.find((i) => i.index === it.fileIndex);
          ragSearchResult.push({
            ...it,
            filename: file?.name || '',
            fileUrl: file?.url || ''
          });
        });
      }

      useDirectUserInput = false;
    } else {
      // 当没有需要走 rag 的文件时，则直接使用用户输入
      useDirectUserInput = true;
    }
  }
  // 其他模式
  else {
    quoteQAList.forEach((it, index) => {
      const quoteSegment = convert2QuoteQAStr(it, index, advancedQuote);
      quoteQA.push(quoteSegment);

      const isCode = isCodeRagSearchResult(it);
      // 如果是代码，则需要获取仓库路径
      if (isCode) {
        repoName = it.repoName;
      }

      // 如果开启了增强知识引用，则根据是否是代码来决定使用哪种知识引用类型
      if (advancedQuote) {
        quoteType = isCode ? 'code' : 'docs';
      }
    });
  }

  logger.info(`systemPrompt2: ${systemPrompt}`);

  let quoteSeparator = '\n------\n';
  if (advancedQuote) {
    quoteSeparator = quoteType === 'code' ? '\n' : '\n<hr>\n\n';
  }

  const { quoteText } = await filterQuote({
    quoteQA,
    userChatInput,
    maxTokens,
    quoteType,
    quoteSeparator
  });

  const userInputChatItem = await combineUserInputWithQuote({
    userChatInput,
    // 如果使用 rag 来处理上传的文件，则不需要再 messages 中直接放入文本内容，后续会走 rag 处理
    inputFiles: enableRagForInputFiles
      ? // 如果模型支持图片，则只将图片放入 messages 中
        inputFiles.filter((f) => f.type === ChatFileTypeEnum.image && model.supportImg)
      : inputFiles,
    useDirectUserInput,
    quotePrompt,
    quoteText,
    quoteType,
    repoName
  });

  logger.info(`systemPrompt3: ${systemPrompt}`);

  // 如果开启了增强知识引用，并且 quoteQA 不为空，则使用增强知识引用系统提示词
  if (advancedQuote && quoteQA.length > 0) {
    const advancedSystemPrompt = await getQuoteSystemPromptFromKconf(quoteType);
    if (advancedSystemPrompt) {
      systemPrompt = advancedSystemPrompt;
    }
  }
  logger.info(`original messages length ${histories.length}`);
  logger.info(`original histories: ${JSON.stringify(histories)}`);
  logger.info(`original userInputChatItem: ${JSON.stringify(userInputChatItem)}`);
  logger.info(`original systemPrompt: ${systemPrompt}`);

  const messages: ChatItemType[] = [
    ...getSystemPrompt(systemPrompt),
    ...histories,
    userInputChatItem
  ];
  const adaptMessages = chats2GPTMessages({
    messages,
    reserveId: false,
    supportImg: model.supportImg,
    maxContextLength: model.maxContext
  });
  logger.info(`adaptMessages length ${adaptMessages.length}`, adaptMessages);

  const formattedGPTMessages = formatGPTMessagesInRequestBefore(adaptMessages);

  logger.info(`formattedGPTMessages length ${formattedGPTMessages.length}`, formattedGPTMessages);

  const loadMessages = await Promise.all(
    formattedGPTMessages.map(async (item) => {
      if (item.role === ChatCompletionRequestMessageRoleEnum.User) {
        return {
          ...item,
          content: await loadChatImgToBase64(item.content)
        };
      } else {
        return item;
      }
    })
  );

  return {
    messages: loadMessages,
    ragFileSearchResult: ragSearchResult,
    enableRagForInputFiles
  };
}

export const getPlainTextMessages = (messages: ChatItemType[]) => {
  const results: Array<
    | {
        role: 'user';
        content: string;
      }
    | {
        role: 'assistant';
        content: string;
        artifacts: ArtifactChatItem[];
      }
  > = [];
  messages.forEach((item) => {
    if (item.obj === ChatRoleEnum.Human) {
      const contents: string[] = [];
      item.value.forEach((item) => {
        if (item.type === ChatItemValueTypeEnum.text) {
          contents.push(item.text?.content || '');
        }
      });

      results.push({
        role: 'user',
        content: contents.join('\n')
      });
    } else if (item.obj === ChatRoleEnum.AI) {
      const contents: string[] = [];
      item.value.forEach((value) => {
        if (value.type === ChatItemValueTypeEnum.text) {
          contents.push(value.text?.content || '');
        }
      });

      const artifacts = item[DispatchNodeResponseKeyEnum.artifacts] || [];

      results.push({
        role: 'assistant',
        content: contents.join('\n'),
        artifacts
      });
    }
  });
  return results;
};

export const emitQuoteSource = (emitter: DispatchEvenEmitter, quoteSource: QuoteSource[]) => {
  responseWrite({
    write: (t) => emitter.emit('data', t),
    event: SseResponseEventEnum.quoteSource,
    data: JSON.stringify({
      list: quoteSource
    })
  });
};
