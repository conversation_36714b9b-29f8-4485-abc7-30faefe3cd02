import axios from 'axios';
import { getOneProxy } from '../../../common/proxy';
import { isIP } from 'net';
import { WorkflowIOValueTypeEnum } from '@fastgpt/global/core/workflow/constants';
import { valueTypeFormat } from './utils';
import { CensorData, censorHttpMessage } from '../../../common/censor/http';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { IS_OVERSEA } from '@fastgpt/global/core/chat/constants';
import { getOverseaInternalApiWhitelist } from '../../../common/kconf';
import { installDNSCache } from '@fastgpt/global/common/dns';

installDNSCache();
const UNDEFINED_SIGN = 'UNDEFINED_SIGN';

const logger = createLogger('workflow-dispatch-http-utils');

export async function fetchData({
  ksapAuthnToken,
  method,
  url,
  headers,
  body,
  params,
  type,
  username,
  appId
}: {
  ksapAuthnToken: string;
  method: string;
  url: string;
  headers: Record<string, any>;
  body: Record<string, any>;
  params: Record<string, any>;
  type: CensorData['type'];
  username: string;
  appId: string;
}): Promise<Record<string, any>> {
  const uri = new URL(url);
  if (isIP(uri.hostname) || uri.hostname === 'localhost') {
    throw new Error('不支持使用 IP 或 localhost');
  }

  const innerTokens: { 'x-ksap-authn-token'?: string } = {};
  if (
    ksapAuthnToken &&
    uri.hostname === 'frontend-cloud.internal' &&
    uri.pathname === '/kwaipilot/gateway'
  ) {
    innerTokens['x-ksap-authn-token'] = ksapAuthnToken;
    logger.info(`fetchData set ksap token x-ksap-authn-token length ${ksapAuthnToken.length}`);
  }
  if (!IS_OVERSEA) {
    // 上报 http 请求数据
    censorHttpMessage({
      requestedAt: Date.now(),
      username,
      type,
      appId,
      url,
      headers,
      method,
      body
    });
  }

  // 海外服务调用内部接口，需要检查是否在内部服务白名单
  if (
    IS_OVERSEA &&
    (uri.hostname.endsWith('.internal') || uri.hostname.endsWith('.corp.kuaishou.com'))
  ) {
    const whitelistRules = await getOverseaInternalApiWhitelist();
    const isWhitelisted = whitelistRules.some((rule) => {
      const { host, paths } = rule;
      return uri.hostname === host && paths.some((path) => uri.pathname.startsWith(path));
    });

    if (!isWhitelisted) {
      throw new Error('不支持访问内部服务');
    }
  }

  const proxy = await getOneProxy('foreign', url);
  const { data: response } = await axios({
    method,
    url,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
      ...innerTokens
    },
    timeout: 120000,
    params: params,
    proxy: proxy
      ? {
          protocol: 'http',
          host: proxy.host,
          port: Number(proxy.port)
        }
      : undefined,
    data: ['POST', 'PUT', 'PATCH'].includes(method) ? body : undefined
  });

  /*
    parse the json:
    {
      user: {
        name: 'xxx',
        age: 12
      },
      list: [
        {
          name: 'xxx',
          age: 50
        },
        [{ test: 22 }]
      ],
      psw: 'xxx'
    }

    result: {
      'user': { name: 'xxx', age: 12 },
      'user.name': 'xxx',
      'user.age': 12,
      'list': [ { name: 'xxx', age: 50 }, [ [Object] ] ],
      'list[0]': { name: 'xxx', age: 50 },
      'list[0].name': 'xxx',
      'list[0].age': 50,
      'list[1]': [ { test: 22 } ],
      'list[1][0]': { test: 22 },
      'list[1][0].test': 22,
      'psw': 'xxx'
    }
  */
  const parseJson = (obj: Record<string, any>, prefix = '') => {
    let result: Record<string, any> = {};

    if (Array.isArray(obj)) {
      for (let i = 0; i < obj.length; i++) {
        result[`${prefix}[${i}]`] = obj[i];

        if (Array.isArray(obj[i])) {
          result = {
            ...result,
            ...parseJson(obj[i], `${prefix}[${i}]`)
          };
        } else if (typeof obj[i] === 'object') {
          result = {
            ...result,
            ...parseJson(obj[i], `${prefix}[${i}].`)
          };
        }
      }
    } else if (typeof obj == 'object') {
      for (const key in obj) {
        result[`${prefix}${key}`] = obj[key];

        if (Array.isArray(obj[key])) {
          result = {
            ...result,
            ...parseJson(obj[key], `${prefix}${key}`)
          };
        } else if (typeof obj[key] === 'object') {
          result = {
            ...result,
            ...parseJson(obj[key], `${prefix}${key}.`)
          };
        }
      }
    }

    return result;
  };

  return {
    formatResponse:
      typeof response === 'object' && !Array.isArray(response) ? parseJson(response) : {},
    rawResponse: response
  };
}

export function replaceVariable(text: string, obj: Record<string, any>) {
  for (const [key, value] of Object.entries(obj)) {
    if (value === undefined) {
      text = text.replace(new RegExp(`{{${key}}}`, 'g'), UNDEFINED_SIGN);
    } else {
      const replacement = JSON.stringify(value);
      const unquotedReplacement =
        replacement.startsWith('"') && replacement.endsWith('"')
          ? replacement.slice(1, -1)
          : replacement;
      text = text.replace(new RegExp(`{{${key}}}`, 'g'), unquotedReplacement);
    }
  }
  return text || '';
}

export function removeUndefinedSign(obj: Record<string, any>) {
  for (const key in obj) {
    if (obj[key] === UNDEFINED_SIGN) {
      obj[key] = undefined;
    } else if (Array.isArray(obj[key])) {
      obj[key] = obj[key].map((item: any) => {
        if (item === UNDEFINED_SIGN) {
          return undefined;
        } else if (typeof item === 'object') {
          removeUndefinedSign(item);
        }
        return item;
      });
    } else if (typeof obj[key] === 'object') {
      removeUndefinedSign(obj[key]);
    }
  }
  return obj;
}

export function formatHttpError(error: any) {
  return {
    message: error?.message,
    name: error?.name,
    method: error?.config?.method,
    baseURL: error?.config?.baseURL,
    url: error?.config?.url,
    code: error?.code,
    status: error?.status
  };
}

export function httpValueTypeFormat(value: any, type?: string) {
  if (!type) {
    return valueTypeFormat(value);
  }

  let formattedType: WorkflowIOValueTypeEnum | undefined = undefined;
  const itemType = type.toLowerCase();
  if (itemType === 'string') {
    formattedType = WorkflowIOValueTypeEnum.string;
  } else if (
    itemType === 'number' ||
    itemType === 'int' ||
    itemType === 'double' ||
    itemType === 'float'
  ) {
    formattedType = WorkflowIOValueTypeEnum.number;
  } else if (itemType === 'boolean') {
    formattedType = WorkflowIOValueTypeEnum.boolean;
  }
  return valueTypeFormat(value, formattedType);
}

// 处理嵌套对象,转换类型
export const nestValueTypeFormat = (value: any, schema: any): any => {
  if (typeof value === 'string' && ['number', 'string', 'boolean'].includes(schema.type)) {
    return httpValueTypeFormat(value, schema.type);
  } else if (typeof value === 'string' && schema.type === 'integer') {
    return httpValueTypeFormat(value, 'number');
  } else if (Array.isArray(value) && schema.items) {
    // 处理数组
    return value.map((item) => nestValueTypeFormat(item, schema.items));
  } else if (typeof value === 'object' && value !== null && schema.properties) {
    // 处理嵌套对象
    const processed: Record<string, any> = {};
    for (const [key, val] of Object.entries(value)) {
      if (schema.properties[key]) {
        processed[key] = nestValueTypeFormat(val, schema.properties[key]);
      } else {
        processed[key] = val;
      }
    }
    return processed;
  }
  return value;
};
