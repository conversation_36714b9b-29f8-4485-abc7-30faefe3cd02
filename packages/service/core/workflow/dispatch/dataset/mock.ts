import type { DatasetSearchResult } from './type';

export const MOCK_DATASET: DatasetSearchResult[] = [
  {
    id: '1',
    title: 'CloudDev 前端开发使用说明',
    link: 'https://docs.corp.kuaishou.com/k/home/<USER>/fcAAgTKOycvaXpdDDm4YdZnDA',
    knowledgeRepoName: 'CloudDev',
    text: `### 1、随时随地开发
居家办公、更换电脑、升级系统？均不用担心环境不一致。
在家、在公司？只要有浏览器，就可以开发。
### 2、解放性能
- 使用云端资源开发，不占用本机编译资源
- 相较旧版 macpro，(编译速度有 30-40% 的提升，依赖安装有 200%-300% 的提升)[https://docs.corp.kuaishou.com/d/home/<USER>
### 3、AI 辅助
**目前内置了 (AI 命令行助手)[https://docs.corp.kuaishou.com/d/home/<USER>
  },
  {
    id: '2',
    title: '云资源与空间管理',
    knowledgeRepoName: 'CloudDev',
    link: 'https://docs.corp.kuaishou.com/k/home/<USER>/fcAB3GFyqE3dWV50uiFhIbEup',
    text: `# **云资源与空间管理**
## 云资源与配额
CloudDev 会为每个用户提供资源配额，让用户能自主创建和管理云空间，保证整体的使用体验。
![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=-7429627161949601298fcABDji840TUggwkjIhqpQYUN&docId=fcAB3GFyqE3dWV50uiFhIbEup)
每个创建的云空间都会占用一定的(云资源)[https://docs.corp.kuaishou.com/k/home/<USER>/fcABbBqf8cC5pf_uzL-HY8dS6#section=h.f77nuz2dupyu]，当资源配额不足时，无法再创建/启动云空间。配额不足时，用户可以通过删除、停止、调整云空间等多种方式来(释放不需要使用的资源)[https://docs.corp.kuaishou.com/k/home/<USER>/fcABbBqf8cC5pf_uzL-HY8dS6#section=h.lullhey9yv8u]，完成云空间的创建。

## 云空间管理
在 CloudDev 上，你可以打开多个 Gitlab 项目，创建多个云空间。
![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=5725449517866086998fcABDji840TUggwkjIhqpQYUN&docId=fcAB3GFyqE3dWV50uiFhIbEup)
除了日常使用外，你还可以通过(停止、启动、删除)[https://docs.corp.kuaishou.com/k/home/<USER>/fcADlahfZsSj2ENq9Qq4e-jDk#section=h.750rl5uoo8aw]等操作来管理你的云空间，释放资源。你也可以查看各个空间的(历史运行情况)[https://docs.corp.kuaishou.com/k/home/<USER>/fcADlahfZsSj2ENq9Qq4e-jDk#section=h.5w7jdj1f9gcl]。
我们也会定期更新云空间环境，优化使用体验，修复环境问题。你可以选择(升级你的云空间)[https://docs.corp.kuaishou.com/k/home/<USER>/fcADlahfZsSj2ENq9Qq4e-jDk#section=h.rcqa545ih8eo]，从而应用上这些优化与修复。`
  },
  {
    id: '3',
    title: '🔥Openv0：让 AI 替你快速开发组件',
    knowledgeRepoName: 'CloudDev',
    link: 'https://docs.corp.kuaishou.com/k/home/<USER>/fcADTMl1coZ_RxqVPeLyLOx0S',
    text: `(访问 CloudDev 平台)[https://clouddev.corp.kuaishou.com/]（云研发）是一个提供开箱即用的云开发环境、让大家能轻松进行在线编码的服务。你可以用它打开 Github 或者内网 Gitlab 上的各种项目，在线查看代码、编辑运行、编译调试，不用占本地电脑。
要打开 Gtihub 项目，可以在搜索栏右侧切换「Github 仓库」进行搜索：
![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=-7705630060766825509fcABLiOE647LOXeXyvPRgvAOb&docId=fcADTMl1coZ_RxqVPeLyLOx0S)
当然，除了 Github 项目，目前更多的用户会创建内网仓库的云开发环境，用它做项目开发。如果你遇到：
- 电脑运行卡顿、发热
- 居家/出差没带笔记本电脑
- 多个需求并行开发
- 联调测试需要固定的内网地址
- ……
不妨试试 CloudDev，这些问题都是它擅长解决的。在搜索框里切换到「内网仓库」，即可搜索并打开公司内的项目：
![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=371744074332191177fcABLiOE647LOXeXyvPRgvAOb&docId=fcADTMl1coZ_RxqVPeLyLOx0S)
除了上面看到的 VSCode IDE 外，CloudDev 根据项目，还提供了 IDEA、AndroidStudio 这些(开箱即用的环境和 IDE)[https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIaCoweLzJiJ-rcj-ZaJem]。
|前端||Java 后端|Android 客户端|C++ 研发||
|------|------|------|------|------|------|
|![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=-4673145667649107499fcAAOresfUkodw9ihC1cZo4sx&docId=fcADTMl1coZ_RxqVPeLyLOx0S)|![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=3216737498542834238fcAAOresfUkodw9ihC1cZo4sx&docId=fcADTMl1coZ_RxqVPeLyLOx0S)|![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=-5990625632798209484fcAAOresfUkodw9ihC1cZo4sx&docId=fcADTMl1coZ_RxqVPeLyLOx0S)|![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=-5890503476748295378fcAAOresfUkodw9ihC1cZo4sx&docId=fcADTMl1coZ_RxqVPeLyLOx0S)|![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=-4673145667649107499fcAAOresfUkodw9ihC1cZo4sx&docId=fcADTMl1coZ_RxqVPeLyLOx0S)|![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=-6797202325588403260fcAAOresfUkodw9ihC1cZo4sx&docId=fcADTMl1coZ_RxqVPeLyLOx0S)|
|(VSCode)[https://docs.corp.kuaishou.com/k/home/<USER>/fcACkB8Kha3lWFeHqqBfnXz9T]|WebStorm|(IDEA)[https://docs.corp.kuaishou.com/k/home/<USER>/fcADa5JxD4yERKMrOghDvmb0W]|(AndroidStudio)[https://docs.corp.kuaishou.com/k/home/<USER>/fcABz2gxa9Tvo1N-sQfzgHQmN]|(VSCode)[https://docs.corp.kuaishou.com/k/home/<USER>/fcACkB8Kha3lWFeHqqBfnXz9T]|(CLion)[https://docs.corp.kuaishou.com/k/home/<USER>/fcAD8ilQIPCIQwi2n1RAVUn7M]|
更多使用说明欢迎参考(开箱即用的环境和 IDE)[https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIaCoweLzJiJ-rcj-ZaJem](开箱即用的环境和 IDE)[https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIaCoweLzJiJ-rcj-ZaJem]。
**![](https://docs.corp.kuaishou.com/image/api/convert/loadimage?id=6702125760298238964fcABLiOE647LOXeXyvPRgvAOb&docId=fcADTMl1coZ_RxqVPeLyLOx0S)**`
  }
];
