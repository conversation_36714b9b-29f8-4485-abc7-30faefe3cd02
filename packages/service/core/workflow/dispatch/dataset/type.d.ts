import { SelectedDatasetTypeV2 } from '@fastgpt/global/core/workflow/api';
import { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { DispatchNodeResultType } from '@fastgpt/global/core/workflow/runtime/type';
import { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type';

export type DatasetSearchProps = ModuleDispatchProps<{
  [NodeInputKeyEnum.datasetSelectListV2]: SelectedDatasetTypeV2;
  // [NodeInputKeyEnum.datasetSimilarity]: number;
  // [NodeInputKeyEnum.datasetMaxTokens]: number;
  // [NodeInputKeyEnum.datasetSearchMode]: `${DatasetSearchModeEnum}`;
  [NodeInputKeyEnum.userChatInput]: string;
  // [NodeInputKeyEnum.datasetSearchUsingReRank]: boolean;
  // [NodeInputKeyEnum.datasetSearchUsingExtensionQuery]: boolean;
  // [NodeInputKeyEnum.datasetSearchExtensionModel]: string;
  // [NodeInputKeyEnum.datasetSearchExtensionBg]: string;
}>;
export type DatasetSearchResponse = DispatchNodeResultType<{
  [NodeOutputKeyEnum.datasetQuoteQAV2]: Array<DatasetSearchResult>;
}>;

export type DatasetSearchResult = {
  id: string;
  text: string;
  link: string;
  title: string;
  knowledgeRepoName: string;
};
