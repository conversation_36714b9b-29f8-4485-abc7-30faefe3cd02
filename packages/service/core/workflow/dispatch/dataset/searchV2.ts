import { DispatchNodeResponseType } from '@fastgpt/global/core/workflow/runtime/type.d';
import { NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { createLogger } from '@fastgpt/global/common/util/logger';
import {
  getLLMServiceConsumer,
  KnowledgeRepoSearchRequest,
  ChatMessage
} from '../../../../common/rpc/llmKwaipilotService/index';
import { responseWrite } from '../../../../common/response';
import { MOCK_DATASET } from './mock';
import { emitQuoteSource, getPlainTextMessages } from '../utils';
import { getGatewayAppLLMCluster } from '../../../../common/kconf';
import { LLM_CLUSTER, LLMCluster } from '@fastgpt/global/common/constants';
import { InternalRagSearchType } from '@fastgpt/global/core/dataset/constants';

import type { ChatItemType } from '@fastgpt/global/core/chat/type';
import type { DatasetSearchProps, DatasetSearchResponse, DatasetSearchResult } from './type';
import type { QuoteSource } from '../type';

export type { DatasetSearchResponse } from './type';

const logger = createLogger('node-dataset-search-v2');

export async function dispatchDatasetSearchV2(
  props: DatasetSearchProps
): Promise<DatasetSearchResponse> {
  const {
    emitter,
    stream,
    histories,
    params: { datasetsV2 = [], userChatInput },
    gatewayAppInfo
  } = props as DatasetSearchProps;

  if (!Array.isArray(datasetsV2)) {
    return Promise.reject('Quote type error');
  }

  if (datasetsV2.length === 0) {
    return Promise.reject('core.chat.error.Select dataset empty');
  }

  if (!userChatInput) {
    return Promise.reject('core.chat.error.User input empty');
  }

  const startTime = Date.now();

  const datasetIds = datasetsV2.map((item) => item.datasetId);

  const result: DatasetSearchResult[] = [];
  try {
    const cluster = await getGatewayAppLLMCluster(gatewayAppInfo);
    const res = await queryDatasets(userChatInput, datasetIds, histories, cluster);
    result.push(...res);
  } catch {}

  if (stream) {
    const quoteSource: Extract<QuoteSource, { type: 'doc' }>[] = result.map((i) => ({
      type: 'doc',
      content: i.text,
      link: i.link,
      title: i.title,
      knowledgeRepoId: i.id,
      knowledgeRepoName: i.knowledgeRepoName
    }));
    emitQuoteSource(emitter, quoteSource);
  }

  const array = result.map((it) => ({
    __type__: InternalRagSearchType.docs,
    ...it
  }));

  if (stream) {
    responseWrite({
      write: (t) => emitter.emit('data', t),
      event: SseResponseEventEnum.datasetSearchResult,
      data: JSON.stringify({
        result: result.map((i) => ({
          id: i.id,
          text: i.text,
          link: i.link,
          title: i.title
        }))
      })
    });
  }

  const responseData: DispatchNodeResponseType = {
    query: userChatInput,
    textOutput: JSON.stringify(result),
    runningTime: Date.now() - startTime
  };

  return {
    [NodeOutputKeyEnum.datasetQuoteQAV2]: array,
    [DispatchNodeResponseKeyEnum.nodeResponse]: responseData
    // [DispatchNodeResponseKeyEnum.toolResponses]: result
  };
}

const queryDatasets = async (
  query: string,
  ids: Array<number>,
  messages: ChatItemType[],
  cluster: LLMCluster
) => {
  if (process.env.DATASET_LOCAL_MOCK) {
    return MOCK_DATASET;
  }

  const consumer = await getLLMServiceConsumer();

  const searchService = consumer.getPromisifyService('LlmServerService');

  const req = new KnowledgeRepoSearchRequest();
  logger.info(`${ids.join(',')} ${query}`);

  req.setKnowledgeRepoIdsList(ids);
  req.setQuery(query);
  req.setPlatform('KwaipilotAgent');
  req.setTopK(3);

  const chatHistories = getPlainTextMessages(messages);
  const results = chatHistories.map((it) => {
    const chatHistory = new ChatMessage();
    chatHistory.setRole(it.role);
    chatHistory.setContent(it.content);
    return chatHistory;
  });

  // 最多使用3对（6条）记录
  req.setChatHistoryList(results.slice(Math.max(results.length - 6, 0)));

  // 是否使用独立集群
  const useDataAsset = cluster === LLM_CLUSTER.DATA_ASSET;
  logger.info(`dataset search cluster ${cluster} ${useDataAsset ? 'useDataAsset' : 'useCommon'}`);
  const res = useDataAsset
    ? await searchService.knowledgeRepoSearchForDataAsset(req)
    : await searchService.knowledgeRepoSearch(req);

  return res.getKnowledgesList().map((it) => ({
    id: it.getKnowledgeRepoId(),
    text: it.getContent(),
    link: it.getLink(),
    title: it.getTitle(),
    knowledgeRepoName: it.getKnowledgeRepoName()
  }));
};
