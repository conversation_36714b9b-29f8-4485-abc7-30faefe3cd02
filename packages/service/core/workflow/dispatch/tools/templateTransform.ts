import axios from 'axios';
import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type/index.d';
import { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { DispatchNodeResponseKeyEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import { DispatchNodeResultType } from '@fastgpt/global/core/workflow/runtime/type';
import { getErrText } from '@fastgpt/global/common/error/utils';
import { IS_PROD } from '@fastgpt/global/core/chat/constants';
import { installDNSCache } from '@fastgpt/global/common/dns';

installDNSCache();
type TemplateTransformProps = ModuleDispatchProps<{
  [NodeInputKeyEnum.textareaInput]: string;
  [key: string]: any;
}>;
type TemplateTransformResponse = DispatchNodeResultType<{
  [NodeOutputKeyEnum.templateTransformOutput]: string;
  [key: string]: any;
}>;

const UNDEFINED_SIGN = 'UNDEFINED_SIGN';

export const dispatchTemplateTransformRequest = async (
  props: TemplateTransformProps
): Promise<TemplateTransformResponse> => {
  let {
    node: { outputs },
    params: {
      [NodeInputKeyEnum.addInputParam]: dynamicInput,
      [NodeInputKeyEnum.textareaInput]: template_input = '',
      ...otherInputs
    }
  } = props;

  try {
    const {
      data: { result, error }
    } = await axios.post<{ result: any; error: string }>(
      IS_PROD
        ? 'http://kwaipilot-tool.internal/code/template/transform'
        : 'https://code-interceptor.staging.kuaishou.com/code/template/transform',
      {
        template: template_input,
        data: otherInputs
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    if (error) {
      throw new Error(error);
    }

    return {
      [DispatchNodeResponseKeyEnum.nodeResponse]: {
        textOutput: result
      },
      [NodeOutputKeyEnum.templateTransformOutput]: result
    };
  } catch (e) {
    const errText = `解析转换失败，失败原因：${getErrText(e)}；输入数据：${JSON.stringify(otherInputs)}`;
    throw new Error(errText);
  }
};
