import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type/index.d';
import axios from 'axios';
import {
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  WorkflowIOValueTypeEnum
} from '@fastgpt/global/core/workflow/constants';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { valueTypeFormat } from '../../utils';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { DispatchNodeResultType } from '@fastgpt/global/core/workflow/runtime/type';
import { getErrText } from '@fastgpt/global/common/error/utils';
import { responseWrite } from '../../../../../common/response';
import { textAdaptGptResponse } from '@fastgpt/global/core/workflow/runtime/utils';
import {
  fetchData,
  formatHttpError,
  httpValueTypeFormat,
  removeUndefinedSign,
  nestValueTypeFormat,
  replaceVariable
} from '../../httpUtils';
import { getToolDetail } from './toolDetail';
import { ToolAuthConfigModel } from './type';
import { installDNSCache } from '@fastgpt/global/common/dns';

installDNSCache();
type PropsArrType = {
  key: string;
  type: string;
  value: string;
};
type KwaipilotToolProps = ModuleDispatchProps<{
  [NodeInputKeyEnum.abandon_httpUrl]: string;
  [NodeInputKeyEnum.httpHeaders]: PropsArrType[];
  [NodeInputKeyEnum.httpParams]: PropsArrType[];
  [NodeInputKeyEnum.httpPath]: PropsArrType[];
  [NodeInputKeyEnum.httpJsonBody]: string;
  [NodeInputKeyEnum.httpReqJsonSchema]: string;
  [NodeInputKeyEnum.versionId]: number;
  [NodeInputKeyEnum.pluginId]: string;
  [NodeInputKeyEnum.addInputParam]: Record<string, any>;
  [key: string]: any;
}>;
type KwaipilotToolResponse = DispatchNodeResultType<{
  [NodeOutputKeyEnum.failed]?: boolean;
  [key: string]: any;
}>;

const logger = createLogger('node-kwaipilot-tool');
export const dispatchKwaipilotToolRequest = async (
  props: KwaipilotToolProps
): Promise<KwaipilotToolResponse> => {
  let {
    user,
    emitter,
    detail,
    appId,
    variables,
    node: { outputs },
    histories,
    ksapAuthnToken,
    params: {
      system_httpHeader: httpHeader = [],
      system_httpParams: httpParams = [],
      system_httpJsonBody: httpJsonBody,
      system_httpPath: httpPath = [],
      httpReqJsonSchema,
      versionId,
      pluginId: tuid,
      [NodeInputKeyEnum.addInputParam]: dynamicInput,
      ...body
    }
  } = props;
  const concatVariables = {
    appId,
    ...variables,
    histories: histories.slice(-10),
    ...body,
    ...dynamicInput
  };

  const allVariables = {
    [NodeInputKeyEnum.addInputParam]: concatVariables,
    ...concatVariables
  };

  let authConfig: ToolAuthConfigModel | undefined;
  let httpReqUrl: string | undefined;
  let httpMethod: string = 'POST';
  try {
    const toolDetail = await getToolDetail(versionId, tuid, user.username);
    authConfig = toolDetail.authConfig;
    httpReqUrl = toolDetail.apiUrl;
    httpMethod = toolDetail.apiMethod;
    logger.info(`get tool detail ${httpReqUrl} ${httpMethod}`, toolDetail);
  } catch (e) {
    logger.error(`get tool detail, ${e}`, e);
    return Promise.reject(`获取工具详情失败，错误内容：${e}`);
  }

  if (!httpReqUrl) {
    logger.error(
      `url empty, authConfig: ${authConfig}, httpReqUrl: ${httpReqUrl}, httpMethod: ${httpMethod}`
    );
    return Promise.reject('工具 URL 为空');
  }
  if (httpPath?.length) {
    let pathVariable: { [key in string]: string } = {};
    httpPath.forEach((item) => {
      pathVariable[item.key] = replaceVariable(item.value, allVariables);
    });
    httpReqUrl = replaceVariable(httpReqUrl, { ...allVariables, ...pathVariable });
  }
  /** 前置鉴权 **/
  const authType = authConfig?.authType || 0;

  if (authType === 1) {
    // 服务认证
    if (authConfig?.location === 'Query' && authConfig.name && authConfig.value) {
      // 补充到 query
      let url = new URL(httpReqUrl);
      let params = new URLSearchParams(url.search);

      params.append(authConfig.name, authConfig.value);
      url.search = params.toString();
      httpReqUrl = url.toString();
    } else if (authConfig?.location === 'Header' && authConfig.name && authConfig.value) {
      // httpHeader[authConfig.name] = authConfig.value;
      httpHeader.push({
        key: authConfig.name,
        type: 'string',
        value: authConfig.value
      });
    }
  } else if (authType === 2 && authConfig?.appKey && authConfig.secretKey) {
    // OPENApi 认证
    const accessToken = await getAccessToken({
      app_key: authConfig.appKey,
      secret_key: authConfig.secretKey
    });
    httpHeader.push({
      key: 'Authorization',
      type: 'string',
      value: `Bearer ${accessToken}`
    });
  }

  // parse header
  const headers = await (() => {
    try {
      if (!httpHeader || httpHeader.length === 0) return {};
      // array
      return httpHeader.reduce((acc: Record<string, string>, item) => {
        const key = replaceVariable(item.key, allVariables);
        const value = replaceVariable(item.value, allVariables);
        acc[key] = valueTypeFormat(value, WorkflowIOValueTypeEnum.string);
        return acc;
      }, {});
    } catch (error) {
      return Promise.reject('core.chat.error.Http header invalid');
    }
  })();
  const params = httpParams.reduce((acc: Record<string, string>, item) => {
    const key = replaceVariable(item.key, allVariables);
    const value = replaceVariable(String(item.value), allVariables);
    acc[key] = valueTypeFormat(value, WorkflowIOValueTypeEnum.string);
    return acc;
  }, {});

  let requestBody: Record<string, any> = {};
  if (httpJsonBody) {
    try {
      const reqJsonSchema = JSON.parse(httpReqJsonSchema);
      const bodyJSONSchema = JSON.parse(reqJsonSchema.bodyParamSchema);
      requestBody = JSON.parse(replaceVariable(httpJsonBody, allVariables));
      requestBody = removeUndefinedSign(requestBody);
      requestBody = nestValueTypeFormat(requestBody, bodyJSONSchema);
    } catch (e) {
      logger.error('body parse error, the reason is ', getErrText(e));
      return Promise.reject('body数据解析失败，失败原因：' + getErrText(e));
    }
  }
  try {
    const { formatResponse, rawResponse } = await fetchData({
      ksapAuthnToken,
      method: httpMethod,
      url: httpReqUrl,
      headers,
      body: requestBody,
      params,
      type: 'tool',
      username: user.username,
      appId
    });

    // format output value type
    const results: Record<string, any> = {};
    for (const key in formatResponse) {
      const output = outputs.find((item) => item.key === key);
      if (!output) continue;
      results[key] = valueTypeFormat(formatResponse[key], output.valueType);
    }

    if (typeof formatResponse[NodeOutputKeyEnum.answerText] === 'string') {
      responseWrite({
        write: (t) => emitter.emit('data', t),
        event: detail ? SseResponseEventEnum.fastAnswer : undefined,
        data: textAdaptGptResponse({
          text: formatResponse[NodeOutputKeyEnum.answerText]
        })
      });
    }

    return {
      [DispatchNodeResponseKeyEnum.nodeResponse]: {
        totalPoints: 0,
        params: Object.keys(params).length > 0 ? params : undefined,
        body: Object.keys(requestBody).length > 0 ? requestBody : undefined,
        headers: Object.keys(headers).length > 0 ? headers : undefined,
        httpResult: rawResponse
      },
      [DispatchNodeResponseKeyEnum.toolResponses]:
        Object.keys(results).length > 0 ? results : rawResponse,
      [NodeOutputKeyEnum.httpRawResponse]: rawResponse,
      ...results
    };
  } catch (error) {
    logger.error('http request error', error);
    return {
      [NodeOutputKeyEnum.failed]: true,
      [DispatchNodeResponseKeyEnum.nodeResponse]: {
        totalPoints: 0,
        params: Object.keys(params).length > 0 ? params : undefined,
        body: Object.keys(requestBody).length > 0 ? requestBody : undefined,
        headers: Object.keys(headers).length > 0 ? headers : undefined,
        httpResult: { error: formatHttpError(error) }
      },
      [NodeOutputKeyEnum.httpRawResponse]: getErrText(error)
    };
  }
};

type Result = {
  accessToken: string;
  refreshToken: string;
  expireAt: number;
};

type RefreshResult = {
  accessToken: string;
  expireTime: number; //有效时间，单位秒
  refreshToken: string;
  appId: string;
};

const domain =
  process.env.NODE_ENV === 'production'
    ? 'http://openapi-gateway.internal'
    : 'https://is-gateway.corp.kuaishou.com';
async function getAccessToken(param: { app_key: string; secret_key: string }): Promise<string> {
  const { app_key, secret_key } = param;

  const { data } = await axios.get<{
    code: number;
    message: string;
    result: RefreshResult;
  }>(`${domain}/token/get`, {
    params: {
      appKey: app_key,
      secretKey: secret_key
    }
  });

  if (data.code) {
    logger.error(`openapi get token error message: ${data.message}`);
    throw new Error(`openapi 获取 token 失败: ${data.message}`);
  }

  // 小于1分钟，则刷新token
  // const { data } = await axios.get<{
  //   code: number;
  //   message: string;
  //   result: RefreshResult;
  // }>(domain + '/token/refresh', {
  //   params: {
  //     appKey: app_key,
  //     secretKey: secret_key,
  //     refreshToken: result.refreshToken,
  //     grantType: 'refresh_token'
  //   }
  // });

  // if (data.code) {
  //   this.logger.error(`openapi update token error message: ${data.message}`);
  //   throw new Error(`openapi 更新 token 失败: ${data.message}`);
  // }
  if (!data.result?.accessToken) {
    const error = 'openapi 获取 accessToken 为空';
    logger.error(`get accessToken error:${error}, ${JSON.stringify(param)}`);
    throw new Error(error);
  }

  const { accessToken, expireTime, refreshToken } = data.result;

  return accessToken;
}
