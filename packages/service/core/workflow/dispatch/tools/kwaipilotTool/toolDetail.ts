import { createLogger } from '@fastgpt/global/common/util/logger';
import {
  getLLMServiceConsumer,
  ToolDetailRequest
} from '../../../../../common/rpc/llmKwaipilotService/index';
import { ToolAuthConfigModel } from './type';

const logger = createLogger('node-kwaipilot-tool-detail');

export const getToolDetail = async (
  id: number,
  tuid: string,
  username: string
): Promise<{ authConfig: ToolAuthConfigModel | undefined; apiUrl: string; apiMethod: string }> => {
  const consumer = await getLLMServiceConsumer();

  const searchService = consumer.getPromisifyService('LlmServerService');

  const req = new ToolDetailRequest();
  logger.info(`tool detail ${id} ${tuid} ${username}`);

  req.setToolId(id);
  req.setTuid(tuid);
  req.setUsername(username);

  const res = await searchService.getToolDetail(req);
  const authConfig = res.getAuthConfig();
  return {
    authConfig: {
      authType: authConfig?.getAuthType(),
      location: authConfig?.getLocation(),
      name: authConfig?.getName(),
      value: authConfig?.getValue(),
      appKey: authConfig?.getAppKey(),
      secretKey: authConfig?.getSecretKey()
    },
    apiMethod: res.getApiMethod(),
    apiUrl: res.getApiPath()
  };
};
