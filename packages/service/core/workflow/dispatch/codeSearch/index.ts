import { DispatchNodeResponseType } from '@fastgpt/global/core/workflow/runtime/type.d';
import { NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { createLogger } from '@fastgpt/global/common/util/logger';
import {
  getLLMServiceConsumer,
  CodeSearchRequest
} from '../../../../common/rpc/llmKwaipilotService/index';
import { responseWrite } from '../../../../common/response';
import { InternalRagSearchType } from '@fastgpt/global/core/dataset/constants';
import { emitQuoteSource } from '../utils';

import type { CodeSearchProps, CodeSearchResponse, CodeSearchResult } from './type';
import type { QuoteSource } from '../type';

const logger = createLogger('node-code-search');

export async function dispatchCodeSearch(props: CodeSearchProps): Promise<CodeSearchResponse> {
  const {
    emitter,
    stream,
    params: { codeSearch = [], userChatInput }
  } = props as CodeSearchProps;

  if (!Array.isArray(codeSearch)) {
    return Promise.reject('Quote code type error');
  }

  if (codeSearch.length === 0) {
    return Promise.reject('core.chat.error.Select dataset empty');
  }

  if (!userChatInput) {
    return Promise.reject('core.chat.error.User input empty');
  }

  const startTime = Date.now();

  // NOTE: 目前只支持一个仓库
  const codeRepo = codeSearch[0];

  const result: CodeSearchResult[] = [];
  try {
    const res = await queryCodeDatasets({
      query: userChatInput,
      repoName: codeRepo.repoName,
      commitId: codeRepo.commitId,
      targetDirectory: codeRepo.targetDirectory
    });
    result.push(...res);
    logger.info(`queryCodeDatasets result: ${JSON.stringify(res)} `);
  } catch (e: unknown) {
    logger.error(`[dispatchCodeSearch] ${e}`);
  }

  if (stream) {
    const quoteSource: Extract<QuoteSource, { type: 'repo' }>[] = result.map((i) => ({
      ...i,
      type: 'repo'
    }));
    emitQuoteSource(emitter, quoteSource);
  }

  const array = result.map((it) => ({
    __type__: InternalRagSearchType.code,
    ...it
  }));

  if (stream) {
    responseWrite({
      write: (t) => emitter.emit('data', t),
      event: SseResponseEventEnum.datasetCodeSearchResult,
      data: JSON.stringify({
        result: result.map((i) => ({ ...i }))
      })
    });
  }

  const responseData: DispatchNodeResponseType = {
    query: userChatInput,
    textOutput: JSON.stringify(result),
    runningTime: Date.now() - startTime
  };

  return {
    [NodeOutputKeyEnum.codeSearchQuotaQA]: array,
    [DispatchNodeResponseKeyEnum.nodeResponse]: responseData
    // [DispatchNodeResponseKeyEnum.toolResponses]: result
  };
}

const queryCodeDatasets = async (data: {
  repoName: string;
  commitId: string;
  targetDirectory: string[];
  query: string;
}): Promise<CodeSearchResult[]> => {
  const consumer = await getLLMServiceConsumer();

  const searchService = consumer.getPromisifyService('LlmServerService');
  const { repoName, commitId, targetDirectory, query } = data;

  const req = new CodeSearchRequest();
  logger.info(`[queryCodeDatasets] ${repoName} ${query} ${commitId} ${targetDirectory.join(',')}`);

  req.setRepoName(repoName);
  req.setCommitId(commitId);
  req.setTargetDirectoryList(targetDirectory);
  req.setQuery(query);

  const res = await searchService.codeSearch(req);
  return res.getListList().map((it) => {
    const path = it.getPath().replace(/^\/+/, '').replace(/\/+$/, '');
    const startLineNo = it.getStartLineNo();
    const endLineNo = it.getEndLineNo();
    const startColNo = it.getStartColNo();
    const endColNo = it.getEndColNo();
    const language = it.getLanguage();
    const segments = path.split('/');
    const fileName = segments[segments.length - 1] ?? '';
    const formattedRepoName = repoName.replace(/^\/+/, '').replace(/\/+$/, '');
    const functionName = it.getFunctionName();
    const functionSignature = it.getFunctionSignature();
    const codeType = it.getCodeType();

    return {
      id: String(it.getId()),
      code: it.getCode(),
      repoName: formattedRepoName,
      startLineNo: startLineNo,
      endLineNo: endLineNo,
      startColNo: startColNo,
      endColNo: endColNo,
      language: language,
      path: path,
      fileName: fileName,
      commitId: commitId,
      functionName: functionName,
      functionSignature: functionSignature,
      codeType: codeType
    };
  });
};
