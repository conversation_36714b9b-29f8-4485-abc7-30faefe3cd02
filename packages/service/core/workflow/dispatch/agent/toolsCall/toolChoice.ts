import { getA<PERSON><PERSON>, NON_STREAMING_TIMEOUT } from '../../../../ai/config';
import {
  ChatCompletion,
  ChatCompletionMessageToolCall,
  ChatCompletionAssistantToolParam,
  ChatCompletionMessageParam,
  ChatCompletionChunk
} from '@fastgpt/global/core/ai/type';
import { responseWrite } from '../../../../../common/response';
import { SseResponseEventEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import { textAdaptGptResponse } from '@fastgpt/global/core/workflow/runtime/utils';
import { ChatCompletionRequestMessageRoleEnum } from '@fastgpt/global/core/ai/constants';
import { ToolNodeItemType, ToolsCallKit } from './type.d';
import { getToolName } from './utils';
import { DispatchEvenEmitter } from '@fastgpt/global/core/workflow/type';
import { Stream } from 'openai/streaming';

async function emitStreamResponse({
  emitter,
  detail,
  toolNodes,
  stream
}: {
  emitter: DispatchEvenEmitter;
  detail: boolean;
  toolNodes: ToolNodeItemType[];
  stream: Stream<ChatCompletionChunk>;
}) {
  const write = (t: string) => emitter.emit('data', t);

  let textAnswer = '';
  let toolCalls: ChatCompletionMessageToolCall[] = [];
  const calls: ChatCompletionMessageToolCall[] = [];

  for await (const part of stream) {
    if (emitter.closed) {
      stream.controller?.abort();
      break;
    }

    const responseChoice = part.choices?.[0]?.delta;
    // console.log(JSON.stringify(responseChoice, null, 2));
    if (responseChoice?.content) {
      const content = responseChoice.content || '';
      textAnswer += content;

      responseWrite({
        write,
        event: detail ? SseResponseEventEnum.answer : undefined,
        data: textAdaptGptResponse({
          text: content
        })
      });
    } else if (responseChoice?.tool_calls?.[0]) {
      const toolCall: ChatCompletionMessageToolCall = responseChoice
        .tool_calls[0] as ChatCompletionMessageToolCall;
      calls.push(toolCall);

      // 流响应中,每次只会返回一个工具. 如果带了 id，说明是执行一个工具
      if (toolCall.id) {
        const toolNode = toolNodes.find((item) => item.nodeId === toolCall.function?.name);

        if (toolNode) {
          if (toolCall.function?.arguments === undefined) {
            toolCall.function.arguments = '';
          }
          toolCalls.push({
            ...toolCall,
            toolName: toolNode.name,
            toolAvatar: toolNode.avatar
          });

          if (detail) {
            responseWrite({
              write,
              event: SseResponseEventEnum.toolCall,
              data: JSON.stringify({
                tool: {
                  id: toolCall.id,
                  toolName: getToolName(toolNode),
                  toolAvatar: toolNode.avatar,
                  functionName: toolCall.function.name,
                  params: toolCall.function.arguments,
                  response: ''
                }
              })
            });
          }
        }
      }
      /* arg 插入最后一个工具的参数里 */
      const arg: string = responseChoice.tool_calls?.[0]?.function?.arguments ?? '';
      const currentTool = toolCalls[toolCalls.length - 1];
      if (currentTool) {
        currentTool.function.arguments += arg;

        if (detail) {
          responseWrite({
            write,
            event: SseResponseEventEnum.toolParams,
            data: JSON.stringify({
              tool: {
                id: currentTool.id,
                toolName: '',
                toolAvatar: '',
                params: arg,
                response: ''
              }
            })
          });
        }
      }
    }
  }

  if (!textAnswer && toolCalls.length === 0) {
    return Promise.reject('LLM api response empty');
  }

  return {
    answer: textAnswer,
    originalAnswer: textAnswer,
    toolCalls,
    calls
  };
}

const processToolRequest: ToolsCallKit['processToolRequest'] = async ({
  model,
  requestBody,
  stream,
  toolNodes,
  emitter,
  detail
}) => {
  const ai = await getAIApi({
    model,
    timeout: NON_STREAMING_TIMEOUT
  });

  const aiResponse = await ai.chat.completions.create(requestBody, {
    headers: {
      Accept: 'application/json, text/plain, */*'
    }
  });

  if (stream) {
    return emitStreamResponse({
      emitter,
      detail,
      toolNodes,
      stream: aiResponse as Stream<ChatCompletionChunk>
    });
  } else {
    const result = aiResponse as ChatCompletion;
    const calls = result.choices?.[0]?.message?.tool_calls || [];

    // 加上name和avatar
    const toolCalls = calls.map((tool) => {
      const toolNode = toolNodes.find((item) => item.nodeId === tool.function?.name);
      return {
        ...tool,
        toolName: toolNode?.name || '',
        toolAvatar: toolNode?.avatar || ''
      };
    });

    const answer = result.choices?.[0]?.message?.content || '';
    return {
      answer,
      originalAnswer: answer,
      toolCalls: toolCalls,
      calls
    };
  }
}

const concatCompleteMessages: ToolsCallKit['concatCompleteMessages'] = ({
  messages,
  toolCalls,
  toolsRunResponse
}) => {
  const concatToolMessages = [...messages] as ChatCompletionMessageParam[];
  // Run the tool, combine its results, and perform another round of AI calls
  if (toolCalls.length > 0) {
    const assistantToolMsgParams: ChatCompletionAssistantToolParam = {
      role: ChatCompletionRequestMessageRoleEnum.Assistant,
      tool_calls: toolCalls
    };
    concatToolMessages.push(assistantToolMsgParams);
  }
  const completeMessages = [
    ...concatToolMessages,
    ...toolsRunResponse.map((item) => item?.toolMsgParams)
  ];

  return completeMessages;
}

export const toolsCallKit: ToolsCallKit = {
  processToolRequest,
  concatCompleteMessages,
  formatMessagesBeforeSend: ({ messages }) => {
    return {
      formattedMessages: messages,
      withToolsInPrompt: false
    }
  }
}
