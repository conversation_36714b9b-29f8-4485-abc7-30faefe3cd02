import { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { DispatchNodeResponseKeyEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import type {
  DispatchNodeResultType,
  RuntimeNodeItemType
} from '@fastgpt/global/core/workflow/runtime/type';
import { ModelTypeEnum, getRemoteLLMModel } from '../../../../ai/model';
import { filterToolNodeIdByEdges, getFormattedGPTMessages } from '../../utils';
import { DispatchToolModuleProps, ToolNodeItemType } from './type.d';
import { GPTMessages2Chats } from '@fastgpt/global/core/chat/adapt';
import { formatModelChars2Points } from '../../../../../support/wallet/usage/utils';
import { getHistoryPreview } from '@fastgpt/global/core/chat/utils';
import { runToolsCallFlow } from './flow';
import { createLogger } from '@fastgpt/global/common/util/logger';

type Response = DispatchNodeResultType<{
  [NodeOutputKeyEnum.answerText]: string;
}>;

const logger = createLogger('node-tools-call');

export const dispatchToolsCall = async (props: DispatchToolModuleProps): Promise<Response> => {
  const {
    node: { nodeId, name, outputs },
    runtimeNodes,
    runtimeEdges,
    inputFiles: files = [],
    histories,
    params: {
      model,
      systemPrompt,
      userChatInput,
      history = 6,
      quoteQA: quoteQAOriginal,
      quotePrompt
    }
  } = props;

  const toolModel = await getRemoteLLMModel(model);
  if (!toolModel) {
    return Promise.reject(`当前选择的 ${model} 模型无法使用，请选择其他模型`);
  }

  /* get tool params */
  const toolNodeIds = filterToolNodeIdByEdges({ nodeId, edges: runtimeEdges });

  // Gets the module to which the tool is connected
  const toolNodes = toolNodeIds
    .map((nodeId) => {
      const tool = runtimeNodes.find((item) => item.nodeId === nodeId);
      return tool;
    })
    .filter(Boolean)
    .map<ToolNodeItemType>((tool) => {
      const toolParams =
        tool?.inputs.filter(
          (input) => !!input.toolDescription && input.key !== NodeInputKeyEnum.toolSelect
        ) || [];
      return {
        ...(tool as RuntimeNodeItemType),
        toolParams
      };
    });

  logger.info(`tool nodes ${userChatInput}`, toolNodes);

  const maxFilterTokens = toolModel.maxContext - (toolModel.toolChoice ? 300 : 500);
  const { messages: gptMessages } = await getFormattedGPTMessages({
    history,
    quoteQAOriginal,
    histories,
    userChatInput,
    model: toolModel,
    quotePrompt,
    inputFiles: files,
    systemPrompt,
    useInputFiles: true,
    advancedQuote: false,
    maxTokens: maxFilterTokens
  });

  const {
    dispatchFlowResponse, // tool flow response
    totalTokens,
    completeMessages = [], // The actual message sent to AI(just save text)
    assistantResponses = [] // FastGPT system store assistant.value response
  } = await (async () => {
    return runToolsCallFlow(
      {
        ...props,
        toolNodes,
        toolModel,
        messages: gptMessages,
        maxFilterTokens
      },
      1,
      false
    );
  })();

  const { totalPoints, modelName } = formatModelChars2Points({
    model,
    tokens: totalTokens,
    modelType: ModelTypeEnum.llm
  });

  // flat child tool response
  const childToolResponse = dispatchFlowResponse.map((item) => item.flowResponses).flat();

  // concat tool usage
  const totalPointsUsage =
    totalPoints +
    dispatchFlowResponse.reduce((sum, item) => {
      const childrenTotal = item.flowUsages.reduce((sum, item) => sum + item.totalPoints, 0);
      return sum + childrenTotal;
    }, 0);
  const flatUsages = dispatchFlowResponse.map((item) => item.flowUsages).flat();

  return {
    [NodeOutputKeyEnum.answerText]: assistantResponses
      .filter((item) => item.text?.content)
      .map((item) => item.text?.content || '')
      .join(''),
    [DispatchNodeResponseKeyEnum.assistantResponses]: assistantResponses,
    [DispatchNodeResponseKeyEnum.nodeResponse]: {
      totalPoints: totalPointsUsage,
      toolCallTokens: totalTokens,
      model: modelName,
      query: userChatInput,
      historyPreview: getHistoryPreview(GPTMessages2Chats(completeMessages, false)),
      toolDetail: childToolResponse
    },
    [DispatchNodeResponseKeyEnum.nodeDispatchUsages]: [
      {
        moduleName: name,
        totalPoints,
        model: modelName,
        tokens: totalTokens
      },
      ...flatUsages
    ]
  };
};
