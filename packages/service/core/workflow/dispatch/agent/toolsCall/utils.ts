import { FlowNodeInputItemType } from '@fastgpt/global/core/workflow/type/io';
import { ToolNodeItemType } from './type';
import { NodeInputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { FlowNodeTypeEnum } from '@fastgpt/global/core/workflow/node/constant';
import { ChatCompletionMessageParam } from '@fastgpt/global/core/ai/type';
import { RagWebSearchItem } from '@fastgpt/global/core/dataset/type';
import { getRagSearchQuotePrompt } from '../../../../../common/kconf';
import { replaceVariable } from '../../httpUtils';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { webJointTemplate } from '@fastgpt/global/core/workflow/utils';
import type { RAGSearchToolResponse } from '../../../../../common/http/ragSearch';

const logger = createLogger('tools-call-utils');

export const updateToolInputValue = ({
  params,
  inputs
}: {
  params: Record<string, any>;
  inputs: FlowNodeInputItemType[];
}) => {
  return inputs.map((input) => ({
    ...input,
    value: params[input.key] ?? input.value
  }));
};

export const getToolDescription = (node: ToolNodeItemType) => {
  const input = node.inputs.find((i) => i.key === NodeInputKeyEnum.toolSelect);
  if (typeof input?.value === 'string') {
    return input.value;
  } else if (typeof input?.value === 'object' && input?.value?.desc) {
    return input.value.desc;
  }
  return '';
};

export const getToolName = (node: ToolNodeItemType) => {
  if (node.flowNodeType === FlowNodeTypeEnum.kwaipilotTool) {
    const input = node.inputs.find((i) => i.key === NodeInputKeyEnum.toolSelect);
    return input?.value?.name ?? node.name;
  }
  return node.name;
};

export const updateRagWebSearchMessages = async (
  messages: ChatCompletionMessageParam[],
  searchResultContent: string,
  maxFilterTokens: number
): Promise<{
  messages: ChatCompletionMessageParam[];
  searchResult: RagWebSearchItem[];
}> => {
  logger.info('updateRagWebSearchMessages before', { messages, searchResultContent });
  if (!searchResultContent) {
    return {
      messages,
      searchResult: []
    };
  }

  try {
    const result = JSON.parse(searchResultContent) as RAGSearchToolResponse;

    // 如果没有搜索到网页内容，则不更新消息
    if (result.status !== 'success' || result.results.length === 0) {
      return {
        messages,
        searchResult: []
      };
    }

    const searchResult: RagWebSearchItem[] = [];
    let total = 0;
    for (let item of result.results) {
      const count = total + item.content.length;
      if (count > maxFilterTokens) {
        break;
      }
      searchResult.push(item);
      total = count;
    }

    const promptConfig = await getRagSearchQuotePrompt('web');

    const systemPrompt = replaceVariable(promptConfig.systemPrompt, {
      date: new Date().toISOString()
    });

    const nonSystemMessages = messages.filter((i) => i.role !== 'system');
    for (let i = nonSystemMessages.length - 1; i >= 0; i--) {
      const item = nonSystemMessages[i];
      // 把这次新的用户问题，替换为 prompt 模版
      if (item.role === 'user') {
        const userPromptTemplate =
          searchResult.length > 0 ? promptConfig.userPrompt : promptConfig.noQuoteUserPrompt;

        const userContent = replaceVariable(userPromptTemplate, {
          question: item.content,
          quote: searchResult.map((r, idx) => webJointTemplate(idx + 1, r)).join('\n<hr>\n\n')
        });

        item.content = userContent;
        break;
      }
    }

    const systemMessage: ChatCompletionMessageParam = {
      role: 'system',
      content: systemPrompt
    };

    const newMessages = [systemMessage, ...nonSystemMessages];

    logger.info('updateRagWebSearchMessages after', { newMessages });

    return {
      messages: newMessages,
      searchResult
    };
  } catch (e) {
    logger.error('updateRagWebSearchMessages error', { e });
    return {
      messages,
      searchResult: []
    };
  }
};
