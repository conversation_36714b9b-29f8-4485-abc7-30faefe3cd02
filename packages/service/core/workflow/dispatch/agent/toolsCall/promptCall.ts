import { ChatCompletionMessageToolCall } from '@fastgpt/global/core/ai/type';
import { responseWrite } from '../../../../../common/response';
import { SseResponseEventEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import { textAdaptGptResponse } from '@fastgpt/global/core/workflow/runtime/utils';
import { ChatCompletionRequestMessageRoleEnum } from '@fastgpt/global/core/ai/constants';
import { ToolNodeItemType, ToolsCallKit } from './type.d';
import json5 from 'json5';
import { getNanoid, replaceVariable } from '@fastgpt/global/common/string/tools';
import { getToolName } from './utils';
import { createChatCompletion } from '../../chatCompletion/chatCompletion';
import { FINAL_ANSWER_PREFIX, Prompt_Tool_Call, TOOL_PREFIX } from './constants';

const parseSplittedAnswer = (
  str: string,
  findToolNode: (toolId: string) => ToolNodeItemType | null
): ChatCompletionMessageToolCall | string => {
  // 首先，使用正则表达式提取TOOL_ID和TOOL_ARGUMENTS
  str = str.trim();
  const toolString = str.substring(TOOL_PREFIX.length).trim();

  try {
    const toolCall = json5.parse(toolString);
    const toolNode = findToolNode(toolCall.toolId);
    return {
      function: {
        name: toolCall.toolId,
        arguments: JSON.stringify(toolCall.arguments || toolCall.parameters)
      },
      type: 'function',
      id: getNanoid(),
      toolName: toolNode?.name ?? toolCall.toolId,
      toolAvatar: toolNode?.avatar ?? ''
    };
  } catch (error) {
    return str;
  }
};

const parseAnswer = (
  str: string,
  findToolNode: (toolId: string) => ToolNodeItemType | null
): ChatCompletionMessageToolCall[] | string => {
  // 首先，使用正则表达式提取TOOL_ID和TOOL_ARGUMENTS
  str = str.trim();
  if (str.startsWith(TOOL_PREFIX)) {
    const toolAnswers: ChatCompletionMessageToolCall[] = [];
    const toolStrArr = str.split('\n');
    toolStrArr.forEach((str) => {
      const tool = parseSplittedAnswer(str, findToolNode);
      if (typeof tool !== 'string') {
        toolAnswers.push(tool);
      }
    });
    return toolAnswers;
  } else {
    return str;
  }
};

const processToolRequest: ToolsCallKit['processToolRequest'] = async ({
  model,
  requestBody,
  stream,
  toolNodes,
  emitter,
  detail,
  username,
  gatewayAppInfo
}) => {
  const readable = await createChatCompletion(
    {
      ...requestBody,
      stream: true
    },
    {
      username,
      gatewayAppInfo
    }
  );

  const write = (text: string) => emitter.emit('data', text);

  const reader = readable.getReader();
  let answer = '';
  let isFinalAnswer = false;
  let isFirstFinalAnswerChunk = false;

  try {
    while (true) {
      if (emitter.closed) {
        readable.cancel();
        break;
      }

      const ret = await reader.read();
      if (ret.done) {
        break;
      }
      answer += ret.value;
      // 只有设置为返回文本和流时才会写入响应
      if (isFinalAnswer) {
        let content = ret.value;
        // NOTE: 如果使用 RAG 网页搜索，由于提示词被改动，所以不会以 0: 开头，需要特殊处理
        if (isFirstFinalAnswerChunk && answer.startsWith(FINAL_ANSWER_PREFIX)) {
          content = answer.slice(FINAL_ANSWER_PREFIX.length).trimStart();
        }
        isFirstFinalAnswerChunk = false;
        responseWrite({
          write,
          event: detail ? SseResponseEventEnum.answer : undefined,
          data: textAdaptGptResponse({
            text: content
          })
        });
      } else {
        // NOTE: 如果使用 RAG 网页搜索，由于提示词被改动，所以不会以 0: 开头，需要特殊处理
        // 如果以 0: 开头，或者不是以数字开头，或者数字开头后不是跟着「:」，则说明是最终答案
        isFinalAnswer =
          answer.startsWith(FINAL_ANSWER_PREFIX) ||
          /^[^\d]/.test(answer) ||
          /^\d+[^:]\S*/.test(answer);
        isFirstFinalAnswerChunk = true;
      }
    }
  } catch (err) {}

  const findToolNode = (toolId: string) => toolNodes.find((item) => item.nodeId === toolId) ?? null;
  const parseAnswerResultList = parseAnswer(answer, findToolNode);
  if (typeof parseAnswerResultList === 'string') {
    return {
      answer: parseAnswerResultList,
      originalAnswer: parseAnswerResultList,
      toolCalls: [],
      calls: []
    };
  }

  parseAnswerResultList.forEach((tool) => {
    if (stream && detail) {
      const toolNode = findToolNode(tool.function.name);
      responseWrite({
        write: (t) => emitter.emit('data', t),
        event: SseResponseEventEnum.toolCall,
        data: JSON.stringify({
          tool: {
            id: tool.id,
            toolName: toolNode ? getToolName(toolNode) : tool.function.name,
            toolAvatar: toolNode?.avatar,
            functionName: tool.function.name,
            params: tool.function.arguments,
            response: ''
          }
        })
      });
    }
  });

  return {
    answer: '',
    originalAnswer: answer,
    toolCalls: parseAnswerResultList,
    calls: parseAnswerResultList
  };
};

const concatCompleteMessages: ToolsCallKit['concatCompleteMessages'] = ({
  userInputContent,
  messages,
  toolsRunResponse,
  originalAnswer
}) => {
  const clonedMessages = [...messages];
  for (let i = clonedMessages.length - 1; i >= 0; i--) {
    const message = clonedMessages[i];
    if (message.role === ChatCompletionRequestMessageRoleEnum.User) {
      // 在已有提示词后面拼接，会导致模型继续输出 USER ANSWER TOOL_RESPONSE ANSWER 的循环
      // 因此，如果用户输入内容不为空，则将工具调用结果与用户输入直接拼接
      if (userInputContent) {
        message.content = `你可以使用一下这些外部信息：\n${toolsRunResponse.map((t) => t.toolResponsePrompt).join('\n')}\n\n你问题：${userInputContent}`;
      } else {
        message.content = message.content += `${originalAnswer}
TOOL_RESPONSE: """
${toolsRunResponse.map((t) => t.toolResponsePrompt).join('\n')}
"""
ANSWER: 
`;
      }

      break;
    }
  }

  return clonedMessages;
};

const formatMessagesBeforeSend: ToolsCallKit['formatMessagesBeforeSend'] = ({
  messages,
  round,
  tools
}) => {
  // 如果 round > 1，说明已经走过工具调用，直接返回，否则多次 prompt 拼接出的内容会有问题
  if (round === 1) {
    const lastMessage = messages[messages.length - 1];
    if (typeof lastMessage.content !== 'string') {
      throw Error('prompt 工具调用，最后的消息体只支持纯文本');
    }

    const toolsPrompt = JSON.stringify(
      tools.map((t) => {
        return {
          toolId: t.function.name,
          description: t.function.description,
          parameters: {
            type: 'object',
            properties: t.function.parameters?.properties ?? {},
            required: t.function.parameters?.required ?? []
          }
        };
      })
    );
    lastMessage.content = replaceVariable(Prompt_Tool_Call, {
      toolsPrompt,
      // 用户问题中可能已经加入了文件，所以要保留文件内容，不直接填充用户问题
      question: lastMessage.content
    });
  }

  return {
    formattedMessages: messages,
    withToolsInPrompt: true
  };
};

export const toolsCallKit: ToolsCallKit = {
  processToolRequest,
  concatCompleteMessages,
  formatMessagesBeforeSend
};
