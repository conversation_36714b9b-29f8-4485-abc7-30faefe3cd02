export const FINAL_ANSWER_PREFIX = '0:';
export const TOOL_PREFIX = '1:';

export const Prompt_Tool_Call = `<Instruction>
你是一个智能机器人，除了可以回答用户问题外，你还掌握工具的使用能力。有时候，你可以依赖工具的运行结果，来更准确的回答用户。

工具使用了 JSON Schema 的格式声明，其中 toolId 是工具的 description 是工具的描述，parameters 是工具的参数，包括参数的类型和描述，required 是必填参数的列表。

请你根据工具描述，决定回答问题或是使用工具。在完成任务过程中，USER代表用户的输入，TOOL_RESPONSE代表工具运行结果。ANSWER 代表你的输出（你只能返回 ANSWER 的内容，不要返回其他内容）。
你的每次输出都必须以0,1开头，代表是否需要调用工具：
${FINAL_ANSWER_PREFIX} 不使用工具，直接回答内容。
${TOOL_PREFIX} 使用工具，返回工具调用的参数。
如果你选择使用工具，则只需要返回${TOOL_PREFIX} 后面的内容，不需要返回${FINAL_ANSWER_PREFIX} 的内容。
如果你选择不使用工具，则只需要返回${FINAL_ANSWER_PREFIX} 后面的内容，不需要返回${TOOL_PREFIX} 的内容。

例如：

USER: 今天杭州的天气如何
ANSWER: ${TOOL_PREFIX} {"toolId":"testToolId",arguments:{"city": "杭州"}}
TOOL_RESPONSE: """
晴天......
"""
ANSWER: ${FINAL_ANSWER_PREFIX} 今天杭州是晴天。
</Instruction>

现在，我们开始吧！下面是你本次可以使用的工具：

"""
{{toolsPrompt}}
"""

下面是正式的对话内容：

USER: {{question}}
ANSWER: 
`;

export const DEFAULT_MAX_ITERATION = 5;

export const RAG_WEB_SEARCH_TOOL_ID = 'getRelevantSnippets';
