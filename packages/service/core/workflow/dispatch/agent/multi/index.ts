import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { getHistoryPreview } from '@fastgpt/global/core/chat/utils';
// 导入类型
import { responseWrite } from '../../../../../common/response';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { readFromEventSource } from '@fastgpt/global/common/util/fetch-event-source';
import { MultiAgentProps, MultiAgentResponse, AgentWorkflow } from '../../type';
import { getPlainTextMessages } from '../../utils';
import { ChatCompletionChunk } from 'openai/resources';
import { INTERNAL_AGENT_MAP } from '@fastgpt/global/core/workflow/constants';

const KWAIPILOT_AGENT_URL = 'https://ai-gateway.corp.kuaishou.com/v2/chat/completions';

const logger = createLogger('node-agent-multi');

export const dispatchMultiAgent = async (props: MultiAgentProps): Promise<MultiAgentResponse> => {
  let {
    appId,
    emitter,
    stream = false,
    detail = false,
    user,
    histories,
    node: { name },
    inputFiles = [],
    gatewayAppInfo,
    conversationId,
    traceId,
    params: {
      history = 6,
      userChatInput,
      multiAgentIdentifier,
      multiAgentUseInputFiles = true,
      multiAgentSource,
      multiAgentIsResponseText = true
    }
  } = props;
  if (!userChatInput && inputFiles.length === 0) {
    return Promise.reject('没有问题输入，AI 对话无法正常运行');
  }
  if (!multiAgentIdentifier) {
    throw new Error('没有智能体标识，多智能体调用无法正常运行');
  }

  if (!multiAgentSource) {
    throw new Error('没有智能体来源，多智能体调用无法正常运行');
  }

  stream = stream && multiAgentIsResponseText;
  const chatHistories = getPlainTextMessages(histories);
  // 只有节点设置为设置为「返回文本」并且使用流式时（debug模式非流式）才会写入响应
  const needResponseStream = multiAgentIsResponseText && stream;
  // 获取智能体请求地址
  let agentUrl = multiAgentIdentifier;
  let body = '{}';
  let headers: Record<string, string> = {};
  if (multiAgentSource === 'internal') {
    agentUrl = INTERNAL_AGENT_MAP[multiAgentIdentifier];
    body = JSON.stringify({
      appId,
      username: user.username,
      histories: chatHistories,
      inputFiles,
      conversationId,
      question: userChatInput
    });
  } else if (multiAgentSource === 'kwaipilot') {
    agentUrl = KWAIPILOT_AGENT_URL;
    body = JSON.stringify({
      auid: multiAgentIdentifier,
      histories: chatHistories,
      question: userChatInput,
      stream: true
    });
    headers = {
      'x-dmo-username': user.username,
      'x-dmo-provider': 'kwaipilot-agent',
      Authorization: 'Bearer tOfDmnbDbStdES'
    };
  } else if (multiAgentSource === 'external') {
    agentUrl = multiAgentIdentifier;
  } else {
    throw new Error('暂不支持 kwaipilot 智能体来源');
  }

  let answerText = '';
  try {
    const write = (text: string) => emitter.emit('data', text);
    const url = new URL(agentUrl);
    const stream = readFromEventSource(agentUrl, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
        'x-workflow-trace-id': traceId
      },
      body,
      parseData: (data, event) => ({
        data,
        event
      })
    });
    for await (const chunk of stream) {
      if (!chunk.data) {
        continue;
      }

      const { data, event } = chunk;
      // 处理智能体返回的文本
      if (event === SseResponseEventEnum.answer || event === SseResponseEventEnum.fastAnswer) {
        if (data !== '[DONE]') {
          try {
            const chatChunk = JSON.parse(data) as ChatCompletionChunk;
            const text = chatChunk.choices[0].delta.content;
            answerText += text;
          } catch {}
        }
        if (needResponseStream) {
          responseWrite({
            write,
            event: detail ? event : undefined,
            data
          });
        }
      }
      // 处理 artifact 内容
      else if (event === SseResponseEventEnum.artifact) {
        if (needResponseStream) {
          responseWrite({
            write,
            event: detail ? event : undefined,
            data
          });
        }
      }
    }
  } catch (err: unknown) {
    logger.error(`agent url error ${multiAgentIdentifier}`, err);
    throw new Error(`智能体 ${multiAgentIdentifier} 调用错误, ${err}`);
  }

  return {
    answerText,
    [DispatchNodeResponseKeyEnum.nodeResponse]: {
      multiAgentIdentifier,
      multiAgentSource,
      query: `${userChatInput}`,
      historyPreview: getHistoryPreview(histories)
    },
    [DispatchNodeResponseKeyEnum.toolResponses]: answerText,
    history: histories
  };
};
