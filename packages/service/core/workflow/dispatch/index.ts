import { NextApiResponse } from 'next';
import { NodeInputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { DispatchNodeResponseKeyEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import { NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import type { ChatDispatchProps } from '@fastgpt/global/core/workflow/type/index.d';
import type { RuntimeNodeItemType } from '@fastgpt/global/core/workflow/runtime/type.d';
import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type/index.d';
import type {
  AIChatItemValueItemType,
  ChatHistoryItemResType,
  ToolRunResponseItemType
} from '@fastgpt/global/core/chat/type.d';
import { FlowNodeTypeEnum } from '@fastgpt/global/core/workflow/node/constant';
import { replaceVariableAndOutput, getNanoid } from '@fastgpt/global/common/string/tools';
import { responseWriteNodeStatus } from '../../../common/response';
import { getSystemTime } from '@fastgpt/global/common/time/timezone';

import { dispatchWorkflowStart } from './init/workflowStart';
import { dispatchChatCompletion } from './chat/oneapi';
import { dispatchDatasetSearch } from './dataset/search';
import { dispatchDatasetSearchV2 } from './dataset/searchV2';
import { dispatchDatasetConcat } from './dataset/concat';
import { dispatchAnswer } from './tools/answer';
import { dispatchClassifyQuestion } from './agent/classifyQuestion';
import { dispatchContentExtract } from './agent/extract';
import { dispatchHttp468Request } from './tools/http468';
import { dispatchHttpToolRequest } from './tools/httpTool';
import { dispatchKwaipilotToolRequest } from './tools/kwaipilotTool/kwaipilotTool';
import { dispatchTemplateTransformRequest } from './tools/templateTransform';
import { dispatchAppRequest } from './tools/runApp';
import { dispatchQueryExtension } from './tools/queryExternsion';
import { dispatchRunPlugin } from './plugin/run';
import { dispatchPluginInput } from './plugin/runInput';
import { dispatchPluginOutput } from './plugin/runOutput';
import { valueTypeFormat } from './utils';
import {
  filterWorkflowEdges,
  checkNodeRunStatus
} from '@fastgpt/global/core/workflow/runtime/utils';
import { ChatNodeUsageType } from '@fastgpt/global/support/wallet/bill/type';
import { collectNodeDurationLog, createLogger } from '@fastgpt/global/common/util/logger';
import { dispatchToolsCall } from './agent/toolsCall';
import { ChatItemValueTypeEnum } from '@fastgpt/global/core/chat/constants';
import { DispatchFlowResponse } from './type';
import { dispatchStopToolCall } from './agent/toolsCall/stopTool';
import { dispatchLafRequest } from './tools/runLaf';
import { dispatchIfElse } from './tools/runIfElse';
import { RuntimeEdgeItemType } from '@fastgpt/global/core/workflow/type/edge';
import { getReferenceVariableValue } from '@fastgpt/global/core/workflow/runtime/utils';
import { dispatchSystemConfig } from './init/systemConfiig';
import { dispatchCodeInterpreter } from './codeInterpreter';
import { dispatchKconf } from './kconf';
import { dispatchGroupVariable } from './groupVariable';
import { dispatchMultiAgent } from './agent/multi';
import { dispatchCodeSearch } from './codeSearch/index';

const logger = createLogger('node-dispatch');

const callbackMap: Record<`${FlowNodeTypeEnum}`, Function> = {
  [FlowNodeTypeEnum.workflowStart]: dispatchWorkflowStart,
  [FlowNodeTypeEnum.answerNode]: dispatchAnswer,
  [FlowNodeTypeEnum.chatNode]: dispatchChatCompletion,
  [FlowNodeTypeEnum.datasetSearchNode]: dispatchDatasetSearch,
  [FlowNodeTypeEnum.datasetSearchNodeV2]: dispatchDatasetSearchV2,
  [FlowNodeTypeEnum.datasetConcatNode]: dispatchDatasetConcat,
  [FlowNodeTypeEnum.classifyQuestion]: dispatchClassifyQuestion,
  [FlowNodeTypeEnum.contentExtract]: dispatchContentExtract,
  [FlowNodeTypeEnum.httpRequest468]: dispatchHttp468Request,
  [FlowNodeTypeEnum.httpTool]: dispatchHttpToolRequest,
  [FlowNodeTypeEnum.kwaipilotTool]: dispatchKwaipilotToolRequest,
  [FlowNodeTypeEnum.templateTransformNode]: dispatchTemplateTransformRequest,
  [FlowNodeTypeEnum.runApp]: dispatchAppRequest,
  [FlowNodeTypeEnum.pluginModule]: dispatchRunPlugin,
  [FlowNodeTypeEnum.pluginInput]: dispatchPluginInput,
  [FlowNodeTypeEnum.pluginOutput]: dispatchPluginOutput,
  [FlowNodeTypeEnum.queryExtension]: dispatchQueryExtension,
  [FlowNodeTypeEnum.stopTool]: dispatchStopToolCall,
  [FlowNodeTypeEnum.lafModule]: dispatchLafRequest,
  [FlowNodeTypeEnum.ifElseNode]: dispatchIfElse,
  [FlowNodeTypeEnum.kconf]: dispatchKconf,
  [FlowNodeTypeEnum.multiAgent]: dispatchMultiAgent,
  [FlowNodeTypeEnum.codeSearch]: dispatchCodeSearch,

  // none
  [FlowNodeTypeEnum.systemConfig]: dispatchSystemConfig,
  [FlowNodeTypeEnum.emptyNode]: () => Promise.resolve(),
  [FlowNodeTypeEnum.globalVariable]: () => Promise.resolve(),

  [FlowNodeTypeEnum.codeInterpreter]: dispatchCodeInterpreter,
  [FlowNodeTypeEnum.groupVariable]: dispatchGroupVariable,

  [FlowNodeTypeEnum.toolsCall]: dispatchToolsCall
};

/* running */
export async function dispatchWorkFlow({
  emitter,
  runtimeNodes = [],
  runtimeEdges = [],
  histories = [],
  variables = {},
  user,
  stream = false,
  detail = false,
  conversationId,
  traceId,
  appId,
  ...props
}: ChatDispatchProps & {
  runtimeNodes: RuntimeNodeItemType[];
  runtimeEdges: RuntimeEdgeItemType[];
  conversationId: string;
  traceId: string;
}): Promise<DispatchFlowResponse> {
  variables = {
    ...getSystemVariable({ timezone: 'Asia/Shanghai' }),
    ...variables
  };

  let chatResponses: ChatHistoryItemResType[] = []; // response request and save to database
  let chatAssistantResponse: AIChatItemValueItemType[] = []; // The value will be returned to the user
  let chatNodeUsages: ChatNodeUsageType[] = [];
  let toolRunResponse: ToolRunResponseItemType;
  let runningTime = Date.now();
  let debugNextStepRunNodes: RuntimeNodeItemType[] = [];

  /* Store special response field  */
  function pushStore(
    { inputs = [] }: RuntimeNodeItemType,
    {
      answerText = '',
      responseData,
      nodeDispatchUsages,
      toolResponses,
      assistantResponses
    }: {
      [NodeOutputKeyEnum.answerText]?: string;
      [DispatchNodeResponseKeyEnum.nodeResponse]?: ChatHistoryItemResType;
      [DispatchNodeResponseKeyEnum.nodeDispatchUsages]?: ChatNodeUsageType[];
      [DispatchNodeResponseKeyEnum.toolResponses]?: ToolRunResponseItemType;
      [DispatchNodeResponseKeyEnum.assistantResponses]?: AIChatItemValueItemType[]; // tool module, save the response value
    }
  ) {
    const time = Date.now();

    if (responseData) {
      chatResponses.push({
        ...responseData,
        runningTime: +((time - runningTime) / 1000).toFixed(2)
      });
    }
    if (nodeDispatchUsages) {
      chatNodeUsages = chatNodeUsages.concat(nodeDispatchUsages);
      props.maxRunTimes -= nodeDispatchUsages.length;
    }
    if (toolResponses !== undefined) {
      if (Array.isArray(toolResponses) && toolResponses.length === 0) return;
      if (typeof toolResponses === 'object' && Object.keys(toolResponses).length === 0) {
        return;
      }
      toolRunResponse = toolResponses;
    }
    if (assistantResponses) {
      chatAssistantResponse = chatAssistantResponse.concat(assistantResponses);
    } else if (answerText) {
      // save assistant text response
      const isResponseAnswerText =
        inputs.find((item) => item.key === NodeInputKeyEnum.aiChatIsResponseText)?.value ?? true;
      if (isResponseAnswerText) {
        chatAssistantResponse.push({
          type: ChatItemValueTypeEnum.text,
          text: {
            content: answerText
          }
        });
      }
    }

    runningTime = time;
  }
  /* Pass the output of the module to the next stage */
  function nodeOutput(
    node: RuntimeNodeItemType,
    result: Record<string, any> = {}
  ): RuntimeNodeItemType[] {
    pushStore(node, result);

    // Assign the output value to the next node
    node.outputs.forEach((outputItem) => {
      if (result[outputItem.key] === undefined) return;
      /* update output value */
      outputItem.value = result[outputItem.key];
    });

    // Get next source edges and update status
    const skipHandleId = (result[DispatchNodeResponseKeyEnum.skipHandleId] || []) as string[];
    const targetEdges = filterWorkflowEdges(runtimeEdges).filter(
      (item) => item.source === node.nodeId
    );

    // update edge status
    targetEdges.forEach((edge) => {
      if (skipHandleId.includes(edge.sourceHandle)) {
        edge.status = 'skipped';
      } else {
        edge.status = 'active';
      }
    });

    const nextStepNodes = runtimeNodes.filter((node) => {
      return targetEdges.some((item) => item.target === node.nodeId);
    });

    if (props.mode === 'debug') {
      debugNextStepRunNodes = debugNextStepRunNodes.concat(nextStepNodes);
      return [];
    }

    return nextStepNodes;
  }
  function checkNodeCanRun(nodes: RuntimeNodeItemType[] = []): Promise<any> {
    return Promise.all(
      nodes.map((node) => {
        const status = checkNodeRunStatus({
          node,
          runtimeEdges
        });

        if (status === 'run') {
          return nodeRunWithActive(node);
        }
        if (status === 'skip') {
          return nodeRunWithSkip(node);
        }

        return [];
      })
    ).then((result) => {
      const flat = result.flat();
      if (flat.length === 0) return;

      // Update the node output at the end of the run and get the next nodes
      const nextNodes = flat.map((item) => nodeOutput(item.node, item.result)).flat();

      // Remove repeat nodes(Make sure that the node is only executed once)
      const filterNextNodes = nextNodes.filter(
        (node, index, self) => self.findIndex((t) => t.nodeId === node.nodeId) === index
      );

      return checkNodeCanRun(filterNextNodes);
    });
  }
  // 运行完一轮后，清除连线的状态，避免污染进程
  function nodeRunFinish(node: RuntimeNodeItemType) {
    const edges = runtimeEdges.filter((item) => item.target === node.nodeId);
    edges.forEach((item) => {
      item.status = 'waiting';
    });
  }
  /* Inject data into module input */
  function getNodeRunParams(node: RuntimeNodeItemType) {
    const params: Record<string, any> = {};
    node.inputs.forEach((input) => {
      // replace {{}} variables
      let value = replaceVariableAndOutput(input.value, variables, runtimeNodes);

      // replace reference variables
      value = getReferenceVariableValue({
        value,
        nodes: runtimeNodes,
        variables
      });
      // console.log(JSON.stringify(input, null, 2), '=====================');

      // format valueType
      params[input.key] = valueTypeFormat(value, input.valueType);
    });

    return params;
  }
  async function nodeRunWithActive(node: RuntimeNodeItemType) {
    if (emitter.closed || props.maxRunTimes <= 0) {
      logger.info(
        `skip nodeRunWithActive emitter.closed ${emitter.closed} props.maxRunTimes ${props.maxRunTimes}`
      );
      return [];
    }
    const start = Date.now();
    // push run status messages
    if (stream && detail && node.showStatus) {
      responseWriteNodeStatus({
        write: (t) => emitter.emit('data', t),
        name: node.name,
        status: 'running'
      });
    }

    // get node running params
    const params = getNodeRunParams(node);

    const dispatchData: ModuleDispatchProps<Record<string, any>> = {
      ...props,
      appId,
      conversationId,
      traceId,
      emitter,
      variables,
      histories,
      user,
      stream,
      detail,
      node,
      runtimeNodes,
      runtimeEdges,
      params
    };

    try {
      // run module
      const dispatchRes: Record<string, any> = await (async () => {
        if (callbackMap[node.flowNodeType]) {
          logger.info(
            `node.flowNodeType: ${node.flowNodeType}, dispatchData: ${JSON.stringify(dispatchData)}`
          );
          return callbackMap[node.flowNodeType](dispatchData);
        }
        return {};
      })();

      // format response data. Add modulename and module type
      const formatResponseData: ChatHistoryItemResType = (() => {
        if (!dispatchRes[DispatchNodeResponseKeyEnum.nodeResponse]) return undefined;

        let moduleName = node.name;
        if (node.flowNodeType === FlowNodeTypeEnum.httpTool) {
          const input = node.inputs.find((i) => i.key === NodeInputKeyEnum.toolSelect);
          const toolName = input?.value?.name ?? '';
          if (toolName) {
            moduleName = `${moduleName}(${toolName})`;
          }
        }

        return {
          nodeId: node.nodeId,
          moduleName: moduleName,
          moduleType: node.flowNodeType,
          ...dispatchRes[DispatchNodeResponseKeyEnum.nodeResponse]
        };
      })();

      // Add output default value
      node.outputs.forEach((item) => {
        if (!item.required) return;
        if (dispatchRes[item.key] !== undefined) return;
        dispatchRes[item.key] = valueTypeFormat(item.defaultValue, item.valueType);
      });

      nodeRunFinish(node);

      collectNodeDurationLog({
        appId,
        nodeType: node.flowNodeType,
        username: user.username,
        duration: Date.now() - start,
        sessionId: conversationId,
        requestId: traceId,
        beginTimestamp: start,
        endTimestamp: Date.now()
      });
      return {
        node,
        result: {
          ...dispatchRes,
          [DispatchNodeResponseKeyEnum.nodeResponse]: formatResponseData
        }
      };
    } catch (error: unknown) {
      throw {
        error,
        node
      };
    }
  }
  async function nodeRunWithSkip(node: RuntimeNodeItemType) {
    // 其后所有target的节点，都设置为skip
    const targetEdges = runtimeEdges.filter((item) => item.source === node.nodeId);
    nodeRunFinish(node);

    return {
      node,
      result: {
        [DispatchNodeResponseKeyEnum.skipHandleId]: targetEdges.map((item) => item.sourceHandle)
      }
    };
  }

  // start process width initInput
  const entryNodes = runtimeNodes.filter((item) => item.isEntry);

  // reset entry
  runtimeNodes.forEach((item) => {
    item.isEntry = false;
  });
  await checkNodeCanRun(entryNodes);

  // focus try to run pluginOutput
  const pluginOutputModule = runtimeNodes.find(
    (item) => item.flowNodeType === FlowNodeTypeEnum.pluginOutput
  );
  if (pluginOutputModule && props.mode !== 'debug') {
    await nodeRunWithActive(pluginOutputModule);
  }

  return {
    flowResponses: chatResponses,
    flowUsages: chatNodeUsages,
    debugResponse: {
      finishedNodes: runtimeNodes,
      finishedEdges: runtimeEdges,
      nextStepRunNodes: debugNextStepRunNodes
    },
    [DispatchNodeResponseKeyEnum.assistantResponses]:
      mergeAssistantResponseAnswerText(chatAssistantResponse),
    [DispatchNodeResponseKeyEnum.toolResponses]: toolRunResponse
  };
}

/* sse response modules staus */
export function responseStatus({
  res,
  status,
  name
}: {
  res: NextApiResponse;
  status?: 'running' | 'finish';
  name?: string;
}) {
  if (!name) return;
  responseWriteNodeStatus({
    res,
    name
  });
}

/* get system variable */
export function getSystemVariable({ timezone }: { timezone: string }) {
  return {
    cTime: getSystemTime(timezone)
  };
}

/* Merge consecutive text messages into one */
export const mergeAssistantResponseAnswerText = (response: AIChatItemValueItemType[]) => {
  const result: AIChatItemValueItemType[] = [];
  // 合并连续的text
  for (let i = 0; i < response.length; i++) {
    const item = response[i];
    if (item.type === ChatItemValueTypeEnum.text) {
      let text = item.text?.content || '';
      const lastItem = result[result.length - 1];
      if (lastItem && lastItem.type === ChatItemValueTypeEnum.text && lastItem.text?.content) {
        lastItem.text.content += text;
        continue;
      }
    }
    result.push(item);
  }

  return result;
};
