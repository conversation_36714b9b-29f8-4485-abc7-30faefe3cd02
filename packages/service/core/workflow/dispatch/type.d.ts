import {
  AIChatItemValueItemType,
  ChatHistoryItemResType,
  ChatItemValueItemType,
  ToolRunResponseItemType
} from '@fastgpt/global/core/chat/type';
import { DispatchNodeResponseKeyEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import { RuntimeNodeItemType } from '@fastgpt/global/core/workflow/runtime/type';
import { RuntimeEdgeItemType } from '@fastgpt/global/core/workflow/type/edge';
import { ChatNodeUsageType } from '@fastgpt/global/support/wallet/bill/type';
import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type/index.d';
import type {
  AIChatNodeProps,
  MultiAgentNodeProps
} from '@fastgpt/global/core/workflow/runtime/type.d';
import { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { DispatchNodeResultType } from '@fastgpt/global/core/workflow/runtime/type';

export type DispatchFlowResponse = {
  flowResponses: ChatHistoryItemResType[];
  flowUsages: ChatNodeUsageType[];
  debugResponse: {
    finishedNodes: RuntimeNodeItemType[];
    finishedEdges: RuntimeEdgeItemType[];
    nextStepRunNodes: RuntimeNodeItemType[];
  };
  [DispatchNodeResponseKeyEnum.toolResponses]: ToolRunResponseItemType;
  [DispatchNodeResponseKeyEnum.assistantResponses]: AIChatItemValueItemType[];
};

export type ChatProps = ModuleDispatchProps<
  AIChatNodeProps & {
    [NodeInputKeyEnum.userChatInput]: string;
    [NodeInputKeyEnum.history]?: ChatItemType[] | number;
    // [NodeInputKeyEnum.aiChatDatasetQuote]?: SearchDataResponseItemType[];
    [NodeInputKeyEnum.aiChatDatasetQuote]?: unknown;
  }
>;
export type ChatResponse = DispatchNodeResultType<{
  [NodeOutputKeyEnum.answerText]: string;
  [NodeOutputKeyEnum.history]: ChatItemType[];
}>;
export type MultiAgentResponse = DispatchNodeResultType<{
  [NodeOutputKeyEnum.answerText]: string;
  [NodeOutputKeyEnum.history]: ChatItemType[];
}>;
export type MultiAgentProps = ModuleDispatchProps<
  MultiAgentNodeProps & {
    [NodeInputKeyEnum.userChatInput]: string;
    [NodeInputKeyEnum.history]?: ChatItemType[] | number;
  }
>;

export type AgentWorkflow = {
  type: string;
  reply: string;
  contentType: string;
  sseType: string;
};

export type QuoteSource =
  | {
      type: 'repo';
      code: string;
      startLineNo: number;
      endLineNo: number;
      startColNo: number;
      endColNo: number;
      language: string;
      path: string;
      repoName: string;
      fileName: string;
      commitId: string;
    }
  | {
      type: 'web';
      title: string;
      link: string;
      date: string;
      favicon: string;
      content: string;
      prevContent: string;
      nextContent: string;
    }
  | {
      type: 'file';
      filename: string;
      url: string;
      pageNum: number;
      content: string;
    }
  | {
      type: 'doc';
      content: string;
      link: string;
      title: string;
      knowledgeRepoId: string;
      knowledgeRepoName: string;
    };
