import { ChatCompletion, ChatCompletionChunk } from 'openai/resources';
import { Stream } from 'openai/streaming';
import { getAIApi, NON_STREAMING_TIMEOUT } from '../../../ai/config';
import { ChatCompletionCreateParamsBase as OpenaiChatCompletionCreateParamsBase } from 'openai/resources/chat/completions';
import { CensorData, censorLLMMessage } from '../../../../common/censor/reporter';
import { GatewayAppInfo } from '@fastgpt/global/core/workflow/type';
import {
  getLLMModelConsumer,
  KwaipilotLlmRequest,
  KwaipilotLlmResponse
} from '../../../../common/rpc/llmKwaipilotModel/index';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { getGatewayAppLLMCluster } from '../../../../common/kconf';

export type ChatCompletionCreateParamsBase = Omit<OpenaiChatCompletionCreateParamsBase, 'stream'>;

export type KwaipilotToolCall = {
  type: string;
  function: {
    arguments: string;
    name: string;
  };
};

export type KwaipilotToolChatCompletionChunk = {
  content: string | null;
  role: string;
  tool_calls?: Array<KwaipilotToolCall>;
};

export type LLMCensorData = {
  username: string;
  gatewayAppInfo: GatewayAppInfo;
};

export type ChatCompletionCreateParamsStream = ChatCompletionCreateParamsBase & {
  stream: true;
};

export type ChatCompletionFn = {
  (params: ChatCompletionCreateParamsBase, censorData: LLMCensorData): Promise<string>;

  (
    params: ChatCompletionCreateParamsStream,
    censorData: LLMCensorData
  ): Promise<ReadableStream<string>>;

  (
    params: ChatCompletionCreateParamsBase & { stream: boolean },
    censorData: LLMCensorData
  ): Promise<ReadableStream<string> | string>;
};

export type ToolChatCompletionFn = {
  (
    params: ChatCompletionCreateParamsBase,
    censorData: LLMCensorData
  ): Promise<KwaipilotToolChatCompletionChunk[]>;

  (
    params: ChatCompletionCreateParamsStream,
    censorData: LLMCensorData
  ): Promise<ReadableStream<KwaipilotToolChatCompletionChunk[]>>;

  (
    params: ChatCompletionCreateParamsBase & { stream: boolean },
    censorData: LLMCensorData
  ): Promise<
    ReadableStream<KwaipilotToolChatCompletionChunk[]> | KwaipilotToolChatCompletionChunk[]
  >;
};

const logger = createLogger('chat-completion-common');
const contentLogger = createLogger('chat-completion-common-content');

export const formatModelType = (modelType: string) => {
  // 目前不需要修复了
  return modelType;
  // // 目前 gpt-3.5-turbo 的窗口大小有问题，暂时使用 gpt-3.5-turbo-16k
  // const formatModel = modelType === 'gpt-3.5-turbo' ? 'gpt-3.5-turbo-16k' : modelType;
  // return formatModel;
};

export const createChatCompletion: ChatCompletionFn = (async (
  {
    messages,
    temperature,
    max_tokens,
    stream,
    model,
    ...others
  }: ChatCompletionCreateParamsBase & {
    stream?: boolean;
  },
  censorData
) => {
  const isKwaipilot = model.startsWith('kwaipilot');

  if (!isKwaipilot) {
    const formatModel = formatModelType(model);
    const ai = await getAIApi({
      model: formatModel,
      timeout: NON_STREAMING_TIMEOUT
    });
    const requestedAt = +new Date();
    const requestBody = {
      model: formatModel,
      temperature,
      messages,
      max_tokens,
      stream,
      ...others
    };
    logger.info(`openai stream request: ${JSON.stringify(requestBody)}`);
    const data = await ai.chat.completions.create(requestBody);
    const reportCensorData: Omit<CensorData, 'finalAnswer'> = {
      userId: censorData.username,
      appInfo: {
        tokenName: censorData.gatewayAppInfo.tokenName,
        tokenOwner: censorData.gatewayAppInfo.tokenOwner,
        id: censorData.gatewayAppInfo.id
      },
      provider: 'openai',
      model: formatModel,
      requestedAt,
      responseStart: +new Date(),
      inputParams: requestBody
    };

    if (!stream) {
      const finalAnswer = (data as ChatCompletion).choices?.[0]?.message.content || '';
      censorLLMMessage({ ...reportCensorData, finalAnswer });
      return finalAnswer;
    }

    const { writable, readable } = new TransformStream<ChatCompletionChunk, string>({
      transform(chunk, controller) {
        contentLogger.info(chunk);
        controller.enqueue(chunk.choices?.[0]?.delta?.content || '');
      }
    });

    const streamData = data as Stream<ChatCompletionChunk>;

    (async () => {
      let finalAnswer = '';
      const writer = writable.getWriter();
      try {
        for await (const chunk of streamData) {
          await writer.write(chunk);
          finalAnswer += chunk?.choices?.[0]?.delta?.content || '';
        }
        await writer.close();
      } catch (err) {
        try {
          await writer.abort();
        } catch (err) {}
      } finally {
        censorLLMMessage({ ...reportCensorData, finalAnswer });
      }
    })();

    return readable;
  }

  const gatewayAppInfo = censorData.gatewayAppInfo;
  const cluster = await getGatewayAppLLMCluster(gatewayAppInfo);
  const consumer = await getLLMModelConsumer(model, cluster);

  const r = new KwaipilotLlmRequest();
  const bodyJsonStr = JSON.stringify({
    messages,
    temperature,
    max_tokens
  });
  logger.info(`kwaipilot stream request: ${bodyJsonStr}`);
  r.setRequestBodyJsonStr(bodyJsonStr);

  const rpcResponse = consumer.getService('KwaipilotLlmApiService').llmChat(r);

  rpcResponse.on('error', (err) => {
    logger.error(`kwaipilot rpc error: ${err}, request: ${bodyJsonStr}`);
  });

  let lastContent = '';

  const { readable, writable } = new TransformStream<KwaipilotLlmResponse, string>({
    transform(chunk, controller) {
      contentLogger.info(chunk);
      const content = (JSON.parse((chunk as KwaipilotLlmResponse).getResponseJsonStr())?.[0]
        ?.content || '') as string;
      controller.enqueue(content.slice(lastContent.length));
      lastContent = content;
    }
  });

  (async () => {
    const writer = writable.getWriter();
    try {
      for await (const chunk of rpcResponse) {
        await writer.write(chunk);
      }
      await writer.close();
    } catch (err) {
      try {
        logger.error(`kwaipilot stream iterator error: ${err}, request: ${bodyJsonStr}`);
        await writer.abort();
      } catch (err) {
        logger.error(`kwaipilot abort stream iterator error: ${err}, request: ${bodyJsonStr}`);
      }
    }
  })();

  if (stream) {
    return readable;
  }

  const reader = readable.getReader();

  let content = '';
  while (true) {
    const result = await reader.read();
    if (result.done) {
      break;
    }
    content += result.value;
  }

  // console.log('----------');
  // console.log(content);

  return content;
}) as ChatCompletionFn;

export const createKwaipilotStreamChatCompletion: ToolChatCompletionFn = (async (
  {
    messages,
    temperature,
    max_tokens,
    stream,
    model,
    tools
  }: ChatCompletionCreateParamsBase & { stream?: boolean },
  censorData
) => {
  const gatewayAppInfo = censorData.gatewayAppInfo;
  const cluster = await getGatewayAppLLMCluster(gatewayAppInfo);
  const consumer = await getLLMModelConsumer(model, cluster);

  const r = new KwaipilotLlmRequest();
  const bodyJsonStr = JSON.stringify({
    messages,
    temperature,
    max_tokens,
    tools
  });
  logger.info(`standalone kwaipilot stream request: ${bodyJsonStr}`);
  r.setRequestBodyJsonStr(bodyJsonStr);

  const rpcResponse = consumer.getService('KwaipilotLlmApiService').llmChat(r);

  let lastContent = '';
  const { readable, writable } = new TransformStream<
    KwaipilotLlmResponse,
    KwaipilotToolChatCompletionChunk[]
  >({
    transform(chunk, controller) {
      contentLogger.info(chunk);
      const delta = JSON.parse(
        (chunk as KwaipilotLlmResponse).getResponseJsonStr()
      ) as Array<KwaipilotToolChatCompletionChunk>;
      if (delta.length === 0) {
        return;
      }
      const first = delta[0];
      const content = first.content || '';
      controller.enqueue([
        {
          ...first,
          content: content.slice(lastContent.length)
        },
        ...delta.slice(1)
      ]);
      lastContent = content;
    }
  });

  (async () => {
    const writer = writable.getWriter();
    try {
      for await (const chunk of rpcResponse) {
        await writer.write(chunk);
      }
      await writer.close();
    } catch (err) {
      try {
        await writer.abort();
      } catch (err) {
        debugger;
      }
    }
  })();

  if (stream) {
    return readable;
  }

  const reader = readable.getReader();

  let chunks: KwaipilotToolChatCompletionChunk[] = [];
  let finalAnswer = '';
  while (true) {
    const result = await reader.read();
    if (result.done) {
      break;
    }

    finalAnswer += result.value[0]?.content ?? '';
    chunks = result.value;
  }

  return chunks;
}) as ToolChatCompletionFn;
