import { filterGPTMessageByMaxTokens, formatGPTMessagesInRequestBefore } from '../../../chat/utils';
import type { ChatCompletionMessageParam } from '@fastgpt/global/core/ai/type.d';
import { formatModelChars2Points } from '../../../../support/wallet/usage/utils';
import {
  ChatCompletionRequestMessageRoleEnum,
  UNLIMITED_MAX_TOKENS
} from '@fastgpt/global/core/ai/constants';
import { countMessagesTokens } from '../../../../common/string/tiktoken/index';
import { GPTMessages2Chats } from '@fastgpt/global/core/chat/adapt';
import { getRemoteLLMModel, ModelTypeEnum } from '../../../ai/model';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { emitQuoteSource, getFormattedGPTMessages } from '../utils';
import { getHistoryPreview } from '@fastgpt/global/core/chat/utils';
// 导入类型
import { createChatCompletion } from '../chatCompletion/chatCompletion';
import { responseWrite } from '../../../../common/response';
import { textAdaptGptResponse } from '@fastgpt/global/core/workflow/runtime/utils';
import { ChatProps, ChatResponse, QuoteSource } from '../type';
import { createLogger } from '@fastgpt/global/common/util/logger';

const logger = createLogger('node-chat-oneapi');

/* request openai chat */
export const dispatchChatCompletion = async (props: ChatProps): Promise<ChatResponse> => {
  let {
    emitter,
    stream = false,
    detail = false,
    user,
    histories,
    node: { name },
    inputFiles = [],
    gatewayAppInfo,
    params: {
      model,
      temperature = 0,
      maxToken: maxTokenSetting = UNLIMITED_MAX_TOKENS,
      history = 6,
      quoteQA: quoteQAOriginal,
      userChatInput,
      useInputFiles = true,
      advancedQuote = false,
      isResponseAnswerText = true,
      systemPrompt = '',
      quotePrompt
    }
  } = props;
  if (!userChatInput && inputFiles.length === 0) {
    return Promise.reject('没有问题输入，AI 对话无法正常运行');
  }
  stream = stream && isResponseAnswerText;

  const modelItem = await getRemoteLLMModel(model, user.username);
  if (!modelItem) {
    return Promise.reject(`当前选择的 ${model} 模型无法使用，请选择其他模型`);
  }

  const responseMaxTokens = isFinite(maxTokenSetting)
    ? Math.min(maxTokenSetting, modelItem.maxResponse)
    : undefined;

  // temperature adapt
  // FastGPT temperature range: 1~10
  temperature = +(modelItem.maxTemperature * (temperature / 10)).toFixed(2);
  temperature = Math.max(temperature, 0.01);

  const {
    messages: gptMessages,
    enableRagForInputFiles,
    ragFileSearchResult
  } = await getFormattedGPTMessages({
    history,
    quoteQAOriginal,
    histories,
    userChatInput,
    model: modelItem,
    quotePrompt,
    inputFiles,
    systemPrompt,
    useInputFiles,
    advancedQuote,
    maxTokens: modelItem.maxContext
  });

  if (enableRagForInputFiles && stream) {
    logger.info(`ragFileSearchResult ${JSON.stringify(ragFileSearchResult)}`);
    const quoteSource: Extract<QuoteSource, { type: 'file' }>[] = ragFileSearchResult.map((i) => ({
      type: 'file',
      filename: i.filename,
      url: i.fileUrl,
      content: i.content,
      pageNum: i.pageNum
    }));
    emitQuoteSource(emitter, quoteSource);
  }

  const concatMessages = [
    ...(modelItem.defaultSystemChatPrompt
      ? [
          {
            role: ChatCompletionRequestMessageRoleEnum.System,
            content: modelItem.defaultSystemChatPrompt
          }
        ]
      : []),
    ...formatGPTMessagesInRequestBefore(gptMessages)
  ] as ChatCompletionMessageParam[];

  if (concatMessages.length === 0) {
    return Promise.reject('core.chat.error.Messages empty');
  }

  const loadMessages = await filterGPTMessageByMaxTokens({
    messages: concatMessages,
    maxTokens: modelItem.maxContext - 300 // filter token. not response maxToken
  });

  logger.info(`loadMessages length ${loadMessages.length} ${userChatInput}`, loadMessages);

  const answer = await createChatCompletion(
    {
      ...modelItem?.defaultConfig,
      model: modelItem.model,
      temperature,
      max_tokens: responseMaxTokens,
      stream,
      messages: loadMessages
    },
    {
      gatewayAppInfo: gatewayAppInfo,
      username: user.username
    }
  );

  const write = (text: string) => emitter.emit('data', text);

  // 只有节点设置为设置为「返回文本」并且使用流式时（debug模式非流式）才会写入响应
  const needResponseStream = isResponseAnswerText && stream;
  let answerText = '';
  if (typeof answer == 'string') {
    answerText = answer;
    if (needResponseStream) {
      responseWrite({
        write,
        event: detail ? SseResponseEventEnum.answer : undefined,
        data: textAdaptGptResponse({
          text: answer
        })
      });
    }
  } else {
    const reader = answer.getReader();

    try {
      while (true) {
        if (emitter.closed) {
          answer.cancel();
          break;
        }

        const ret = await reader.read();
        if (ret.done) {
          break;
        }
        answerText += ret.value;

        // 只有设置为返回文本和流时才会写入响应
        if (needResponseStream) {
          responseWrite({
            write,
            event: detail ? SseResponseEventEnum.answer : undefined,
            data: textAdaptGptResponse({
              text: ret.value
            })
          });
        }
      }
    } catch (err) {}
  }

  const completeMessages = loadMessages.concat({
    role: ChatCompletionRequestMessageRoleEnum.Assistant,
    content: answerText
  });
  const chatCompleteMessages = GPTMessages2Chats(completeMessages);

  const tokens = await countMessagesTokens(chatCompleteMessages);
  const { totalPoints, modelName } = formatModelChars2Points({
    model,
    tokens,
    modelType: ModelTypeEnum.llm
  });

  return {
    answerText,
    [DispatchNodeResponseKeyEnum.nodeResponse]: {
      totalPoints: totalPoints,
      model: modelName,
      tokens,
      query: `${userChatInput}`,
      maxToken: responseMaxTokens,
      historyPreview: getHistoryPreview(chatCompleteMessages),
      contextTotalLen: completeMessages.length
    },
    [DispatchNodeResponseKeyEnum.nodeDispatchUsages]: [
      {
        moduleName: name,
        totalPoints: totalPoints,
        model: modelName,
        tokens
      }
    ],
    [DispatchNodeResponseKeyEnum.toolResponses]: answerText,
    history: chatCompleteMessages
  };
};
