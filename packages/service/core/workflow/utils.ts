import { SearchDataResponseItemType } from '@fastgpt/global/core/dataset/type';
import { countPromptTokens } from '../../common/string/tiktoken/index';
import { getRagSearchQuotePrompt, getSimpleQuotePrompt, QuoteType } from '../../common/kconf';

/* filter search result */
export const filterSearchResultsByMaxChars = async (
  list: SearchDataResponseItemType[],
  maxTokens: number
) => {
  const results: SearchDataResponseItemType[] = [];
  let totalTokens = 0;

  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    totalTokens += await countPromptTokens(item.q + item.a);
    if (totalTokens > maxTokens + 500) {
      break;
    }
    results.push(item);
    if (totalTokens > maxTokens) {
      break;
    }
  }

  return results.length === 0 ? list.slice(0, 1) : results;
};

/* filter search result */
export const filterSearchResultsByMaxChars2 = async (
  list: string[],
  maxTokens: number,
  type: QuoteType
) => {
  const results: string[] = [];
  let totalTokens = await countPromptTokens(await getQuoteUserPromptFromKconf(type));

  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    totalTokens += await countPromptTokens(item);
    if (totalTokens > maxTokens) {
      break;
    }
    results.push(item);
    if (totalTokens > maxTokens) {
      break;
    }
  }

  return results.length === 0 ? list.slice(0, 1) : results;
};

export async function getQuoteUserPromptFromKconf(type: QuoteType): Promise<string> {
  if (type === 'simple') {
    return await getSimpleQuotePrompt();
  }

  const ragSearchQuotePrompt = await getRagSearchQuotePrompt(type);
  return ragSearchQuotePrompt.userPrompt ?? '';
}

export async function getQuoteSystemPromptFromKconf(type: QuoteType): Promise<string> {
  if (type === 'simple') {
    return '';
  }

  const ragSearchQuotePrompt = await getRagSearchQuotePrompt(type);
  return ragSearchQuotePrompt.systemPrompt ?? '';
}
