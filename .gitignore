# dependencies
node_modules/
# next.js
.next/
out/
# production
build/
.astro/
.vscode/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
platform.json
testApi/
local/
dist/

# hugo
**/.hugo_build.lock
docSite/public/
docSite/resources/_gen/
docSite/.vercel
*.local.*


.idea/
files/helm/fastgpt/fastgpt-0.1.0.tgz
files/helm/fastgpt/charts/*.tgz

tmp/

postgres/
mongodb.key
pg-logfile
mongo-data/
projects/app/.env
projects/rpc-provider/lib
projects/rpc-provider/logs
logfile
.rollup.cache
logs
legacy-logs
projects/app/logs

.kfc*